services:
  db:
    image: postgres:latest
    container_name: catalog-db-testing
    environment:
      POSTGRES_DB: cataloging_service_test
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - 5432:5432
    volumes:
      - catalog-db-data-testing:/var/lib/postgresql/data

  redis:
    image: redis:alpine
    container_name: "redis"
    ports:
      - 6379:6379


volumes:
  catalog-db-data-testing: