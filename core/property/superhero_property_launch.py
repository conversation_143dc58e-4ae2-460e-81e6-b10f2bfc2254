import datetime
import json
import logging
import uuid
from decimal import Decimal

from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils
from cataloging_service.api.v3.property_launch import PropertyLaunchSchema
from cataloging_service.constants import constants, error_codes
from cataloging_service.constants.constants import DEFAULT_COST_CENTER_ID_BRAND_CODE
from cataloging_service.constants.model_choices import (
    PropertyChoices,
    PropertyDetailChoices,
    RoomTypeConfigurationChoices,
    StandardStatusChoices,
)
from cataloging_service.domain import service_provider
from cataloging_service.domain.enum_config_service import EnumConfigService
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.repositories import repo_provider, TenantConfigRepository
from cataloging_service.models import (
    Property,
    Location,
    PropertyDetail,
    PropertySku,
    Room,
    RoomRackRateModel,
    RoomTypeConfiguration,
    RoomType,
    Owner,
    Ownership,
    GuestFacingProcess,
    Brand,
    PropertyBrandMapping,
    NewRatePlanConfig,
    NewRatePlan,
    Sku,
    TenantConfigModel,
    BankDetail,
    Country,
    State,
    City,
    Param,
)
from cataloging_service.utils import Utils
from core.property.superhero_property_amenities import create_property_amenities
from core.property.superhero_room_amenities import create_room_amenities

logger = logging.getLogger(__name__)

INDEPENDENT_BRAND_CODE = "independent"
IS_PARTNER_PRICING_ENABLED = True  # for independent properties


def launch_property(property_launch):
    try:
        property = create_property(property_launch)
        location = create_location(property, property_launch)
        property_detail = create_property_detail(property, property_launch)
        bank_details = create_property_bank_details(property_detail, property_launch)

        room_types = create_room_types(property_launch)
        if len(room_types) > 0:
            repo_provider.property_repository.persist_all(room_types)

        room_types = [
            {"type": r.type, "id": r.id, "code": r.code, "room_type_object": r}
            for r in RoomType.query.all()
        ]

        room_type_configs = [
            create_room_type_config(property, property_detail.provider, detail, room_types)
            for detail in property_launch["room_type_details"]
        ]
        # create room_rack_rate mapping
        room_rack_rate_models = []
        for idx in range(len(property_launch["room_type_tariffs"])):
            tariff = property_launch["room_type_tariffs"][idx]
            detail = property_launch["room_type_details"][idx]
            room_rack_rate_models.extend(
                create_room_rack_rate_model(property, tariff, detail, room_types)
            )

        rooms = [
            create_room(property, detail, room_types, room_type_configs)
            for detail in property_launch["room_details"]
        ]

        owner_details = create_owner_details(property, property_launch)
        guest_facing_detail = create_guest_facing_process(property, property_launch)
        room_amentities = [
            create_room_amenities(property_launch, room, room_types) for room in rooms
        ]
        property_amentities = create_property_amenities(property, property_launch)
        tenant_configs = [
            create_tenant_config(
                property, config_name, property_launch["tenant_configs"][config_name]
            )
            for config_name in property_launch["tenant_configs"]
        ]
        for config_name in property_launch["ar_related_tenant_configs"]:
            tenant_configs.append(
                create_ar_tenant_config(
                    property, config_name, property_launch["ar_related_tenant_configs"][config_name]
                )
            )
        brand = service_provider.property_service.get_brand_for_cost_center_id(property_launch["brand"])
        brand_code = brand.brand_code if (brand and brand.brand_code) else None
        if not brand_code:
            brand_code = TenantConfigRepository().load_v2(config_name=DEFAULT_COST_CENTER_ID_BRAND_CODE)[0].config_value
        property.region = property_launch.get("region")
        city = service_provider.location_service.get_city(property_launch["address"]["city_id"])
        property.cost_center_id = Utils.generate_cost_center_id(property.id,
                                                                brand_code,
                                                                property_launch['region'],
                                                                city.name)

        # create SKUs
        skus = []
        property_skus = []
        sku_categories = service_provider.sku_service.sget_all_sku_categories()
        sku_categories_map = {x.name: x for x in sku_categories}
        property_sku_categories = []
        for sku in property_launch["sku_details"]:
            sku_config, property_sku_config = create_sku_config(property, sku)
            skus.append(sku_config)
            property_skus.append(property_sku_config)
            sku_category = sku_categories_map[sku["category"]]
            property_sku_categories.append(sku_category)

        property.sku_categories = list(set(property_sku_categories))

        repo_provider.property_repository.persist(property)
        repo_provider.property_repository.persist(location)
        repo_provider.property_repository.persist(property_detail)
        repo_provider.property_repository.persist_all(room_type_configs)
        if len(room_rack_rate_models) > 0:
            repo_provider.property_repository.persist_all(room_rack_rate_models)
        # repo_provider.property_repository.persist_all(rate_plan_configs)
        repo_provider.property_repository.persist_all(rooms)
        repo_provider.property_repository.persist_all(owner_details)
        repo_provider.property_repository.persist(guest_facing_detail)
        repo_provider.property_repository.persist_all(room_amentities)
        repo_provider.property_repository.persist_all(property_amentities)
        repo_provider.property_repository.persist_all(tenant_configs)

        repo_provider.sku_repository.persist_all(skus)
        repo_provider.sku_repository.persist_all(property_skus)

        if bank_details:
            repo_provider.property_repository.persist(bank_details)
        if brand:
            property_brand = PropertyBrandMapping(property_id=property.id, brand_id=brand.id)
            repo_provider.property_repository.persist(property_brand)

        service_provider.property_service.set_amenity_summary(property)
        repo_provider.property_repository.session().commit()
        return property, room_type_configs, skus

    except Exception as e:
        logger.exception(e)
        raise


def create_sku_config(property, sku):
    sku_object = Sku(
        name=sku["name"],
        category_id=sku["category_id"],
        hsn_sac=sku["hsn_sac"],
        offering=sku["offering"],
        frequency=sku["frequency"],
        identifier=uuid.uuid4().hex,
    )

    property_sku = PropertySku(
        rack_rate=sku["rack_rate"], status=StandardStatusChoices.ACTIVE, saleable=True, sell_separate=True,
    )
    property_sku.sku = sku_object
    property_sku.property = property
    return sku_object, property_sku


def create_owner_details(property, property_launch):
    owner_details = []
    owner = Owner()
    owner.first_name = property_launch["owner_name"]
    owner.email = property_launch["owner_email"]
    owner.phone_number = property_launch["owner_phone"]

    ownership = Ownership()
    ownership.property = property
    ownership.owner = owner
    ownership.primary = True

    owner_details.append(owner)
    owner_details.append(ownership)

    if property_launch.get("secondary_owner_name"):
        second_owner = Owner()
        second_owner.first_name = property_launch["secondary_owner_name"]
        second_owner.email = property_launch["secondary_owner_email"]
        second_owner.phone_number = property_launch["secondary_owner_phone"]
        owner_details.append(second_owner)
        s_ownership = Ownership()
        s_ownership.owner = second_owner
        s_ownership.property = property
        owner_details.append(s_ownership)

    return owner_details


def create_guest_facing_process(property, property_launch):
    guest_facing_details = GuestFacingProcess()
    guest_facing_details.property = property

    guest_facing_details.checkin_time = property_launch['standard_checkin_time']
    guest_facing_details.checkout_time = property_launch['standard_checkout_time']
    guest_facing_details.free_early_checkin = property_launch['free_early_checkin_time']
    guest_facing_details.free_late_checkout = property_launch['free_late_checkout_time']
    guest_facing_details.early_checkin_fee = property_launch['early_checkin_fee']
    guest_facing_details.late_checkout_fee = property_launch['late_checkout_fee']
    guest_facing_details.system_freeze_time = property_launch.get('system_freeze_time', GuestFacingProcess.three_pm)
    return guest_facing_details


def create_property(property_launch) -> Property:
    property_object = Property()
    property_object.name = property_launch["name"]
    property_object.old_name = property_launch["previous_name"]
    property_object.legal_name = property_launch["legal_name"]
    property_object.status = (
        property_launch["status"]
        if property_launch.get("status")
        else PropertyChoices.STATUS_SIGNED
    )
    property_object.signed_date = property_launch["signed_date"]
    property_object.contractual_launch_date = property_launch["launch_date"]
    property_object.base_currency_code = (
        property_launch.get("base_currency_code")
        if property_launch.get("base_currency_code")
        else CurrencyType.INR.value
    )
    property_object.timezone = property_launch.get("timezone")
    property_object.logo = property_launch.get("hotel_logo")
    property_object.country_code = property_launch.get("country_code")
    set_id(property_object)
    property_object.hx_id = property_object.id
    property_object.current_business_date = dateutils.today()
    return property_object


def set_id(property_object):
    for retry_count in range(constants.MAX_PROPERTY_ID_RETRY):
        pid = Utils.generate_property_id_v3()
        existing_property = repo_provider.property_repository.get_property(pid)
        if existing_property:
            logger.error("Received integrity error while saving property, %s" % property_object)
            if retry_count + 1 == constants.MAX_PROPERTY_ID_RETRY:
                raise CatalogingServiceException(error_codes.PROPERTY_ID_CLASHED)
        else:
            property_object.id = str(pid)
            break


def create_location(property, property_launch):
    location = Location()
    location.latitude = Utils.quantize_and_round_off(
        Decimal(property_launch["latitude"]), decimal_places_to_round_off=6
    )
    location.longitude = Utils.quantize_and_round_off(
        Decimal(property_launch["longitude"]), decimal_places_to_round_off=6
    )

    location.postal_address = (
        property_launch["address"]["line1"] + property_launch["address"]["line2"]
    )
    location.pincode = property_launch["address"]["pincode"]

    if not property_launch["address"].get("city_id"):
        location.city_id = create_city(
            property_launch["address"]["country_name"],
            property_launch["address"]["state_name"],
            property_launch["address"]["city_name"],
        )
    else:
        location.city_id = property_launch["address"]["city_id"]

    location.maps_link = property_launch["google_maps_link"]
    location.property_id = property.id
    location.legal_address = (
        property_launch["legal_address"]["line1"] + property_launch["legal_address"]["line2"]
    )

    legal_pincode = property_launch["legal_address"]["pincode"]
    if legal_pincode:
        location.legal_pincode = legal_pincode

    if not property_launch["legal_address"].get("city_id"):
        location.legal_city_id = create_city(
            property_launch["legal_address"]["country_name"],
            property_launch["legal_address"]["state_name"],
            property_launch["legal_address"]["city_name"],
        )
    else:
        location.legal_city_id = property_launch["legal_address"]["city_id"]

    return location


def create_city(country_name, state_name, city_name):
    country = create_country(country_name)
    state = create_state(country, state_name)
    city = repo_provider.property_location_repository.get_city_by_name(city_name, state.id)
    if not city:
        city = repo_provider.property_location_repository.persist(
            City(name=city_name, state_id=state.id)
        )

    return city.id


def create_country(country_name):
    country = repo_provider.property_location_repository.get_country_by_name(country_name)
    if not country:
        country = repo_provider.property_location_repository.persist(Country(name=country_name))

    return country


def create_state(country, state_name):
    state = repo_provider.property_location_repository.get_state_by_name(state_name, country.id)
    if not state:
        state = repo_provider.property_location_repository.persist(
            State(name=state_name, country_id=country.id)
        )

    return state


def create_property_detail(property, property_launch):
    property_details = PropertyDetail()
    property_details.neighbourhood_type = PropertyDetailChoices.NEIGHBOURHOOD_OTHERS
    property_details.property_type = PropertyDetailChoices.PROPERTY_TYPE_HOTEL
    property_details.property_style = PropertyDetailChoices.PROPERTY_STYLE_OTHERS
    property_details.building_style = PropertyDetailChoices.BUILDING_FLOORS_IN_EACH_BUILDING
    provider = repo_provider.provider_repository.rget_provider_by_code("treebo")
    property_details.provider = provider
    property_details.legal_signature = (
        property_launch.get("legal_signature")
        if property_launch.get("legal_signature")
        else "/reseller/Stamp_White.png"
    )

    property_details.floor_count = property_launch["total_floors"]
    property_details.star_rating = property_launch.get("star_rating", None)
    property_details.construction_year = 0000

    property_details.neighbourhood_detail = ""
    property_details.style_detail = ""
    property_details.reception_landline = property_launch["reception_landline"]
    property_details.reception_mobile = property_launch["reception_mobile"]
    property_details.email = property_launch["email"]
    property_details.previous_franchise = True
    property_details.is_housekeeping_enabled = True
    property_details.is_partner_pricing_enabled = IS_PARTNER_PRICING_ENABLED
    if property_launch.get("pan"):
        property_details.pan = property_launch["pan"]

    property_details.property = property
    if property_launch.get("gstin"):
        property_details.sold_as_id = (
            Param.query.filter(Param.value == constants.MARKETPLACE).first().id
        )
        check_if_valid_gstin(property_launch)
        property_details.gstin = property_launch["gstin"]

    return property_details


def check_if_valid_gstin(property_launch):
    country = repo_provider.property_location_repository.get_country_by_name(
        property_launch["legal_address"]["country_name"]
    )
    state = repo_provider.property_location_repository.get_state_by_name(
        state_name=property_launch["legal_address"]["state_name"], country_id=country.id
    )
    state_code = state.code
    if not state_code:
        raise ValueError("State not found for GSTIN %s." % property_launch["gstin"])

    if int(property_launch["gstin"][:2]) != state_code:
        raise ValueError('First two digits of GSTIN should match state GSTIN code.')


def create_room(property, room_detail, room_types, room_type_configs):
    room = Room()
    room.property = property
    room.room_number = room_detail["room_number"]
    room.building_number = room_detail["building_number"]
    room.floor_number = room_detail["floor_number"]
    room_type = [rt for rt in room_types if rt["type"].upper() == room_detail["room_type"].upper()][
        0
    ]
    room.room_type_id = room_type["id"]
    try:
        room_type_config = [r for r in room_type_configs if r.room_type_id == room.room_type_id][0]
    except IndexError as e:
        raise ValueError(
            "Unsupported room_type give for room: {r}".format(r=room.room_number)
        ) from e
    room.room_type_config = room_type_config
    room.room_size = Decimal(room_type_config.min_room_size)
    return room


def create_rate_plan_config(property, rateplan, room_type_detail, room_types, rateplans):
    rate_plan_config = NewRatePlanConfig()
    rate_plan_config.property_id = property.id
    rate_plan_config.rate_plan_id = [rp["id"] for rp in rateplans if rp["name"] == rateplan][0]
    rate_plan_config.room_type_id = [
        rt["id"] for rt in room_types if rt["type"].upper() == room_type_detail["room_type"].upper()
    ][0]
    return rate_plan_config


def create_tenant_config(property, config_name, config_value):
    tenant_config = TenantConfigModel()
    tenant_config.config_name = config_name
    tenant_config.config_value = str(config_value).replace("'", '"')
    tenant_config.property_id = property.id
    tenant_config.active = True
    return tenant_config


def create_ar_tenant_config(property, config_name, config_value):
    tenant_config = TenantConfigModel()
    tenant_config.config_name = "ar_module." + config_name
    tenant_config.config_value = config_value
    tenant_config.property_id = property.id
    tenant_config.active = True
    return tenant_config


def create_room_types(property_launch):
    room_type_details = property_launch["room_type_details"]
    room_types = {room_type_detail["room_type"] for room_type_detail in room_type_details}
    new_room_types = []

    all_room_types = [roomType.type.upper().strip() for roomType in RoomType.query.all()]
    unique_room_types = [
        roomType for roomType in room_types if roomType.upper().strip() not in all_room_types
    ]

    for unique_room_type in unique_room_types:
        room_type = RoomType(type=unique_room_type, code=Utils.room_type_code_generator("RT"))
        new_room_types.append(room_type)

    return new_room_types


def create_room_type_config(property, provider, room_type_detail, room_types):
    room_config = RoomTypeConfiguration()

    room_type = [
        rt for rt in room_types if rt["type"].upper() == room_type_detail["room_type"].upper()
    ][0]
    room_config.room_type_id = room_type["id"]
    room_config.property_id = property.id
    room_config.provider = provider
    room_config.min_occupancy = 1
    room_config.max_occupancy = "{a}+{c}".format(
        a=room_type_detail["max_adults"], c=room_type_detail["max_children"]
    )
    room_config.extra_bed = RoomTypeConfigurationChoices.EXTRA_BED_MATTRESS
    room_config.adults = int(room_type_detail["max_adults"])
    room_config.mm_id = "{pid}-{rt}".format(pid=property.id, rt=room_type["id"])
    room_config.children = int(room_type_detail["max_children"])
    room_config.max_total = int(room_type_detail["max_total"])
    room_config.ext_room_code = room_type["code"]
    room_config.ext_room_name = room_type["type"]
    room_config.display_name = room_type["type"]
    room_config.min_room_size = Decimal(room_type_detail["room_size"])
    return room_config


def create_room_rack_rate_model(property, room_type_tariff, room_type_detail, room_types):
    room_rack_rates = []
    room_type = [
        rt for rt in room_types if rt["type"].upper() == room_type_detail["room_type"].upper()
    ][0]

    for adult_count in range(int(room_type_detail["max_adults"])):
        room_rack_rate = RoomRackRateModel()
        room_rack_rate.adult_count = adult_count + 1
        room_rack_rate.property_id = property.id
        room_rack_rate.room_type_id = room_type["id"]

        rack_rate = (
            room_type_tariff["single_occupancy_tariff"]
            + adult_count * room_type_tariff["extra_occupancy_tariff"]
        )
        room_rack_rate.rack_rate = rack_rate
        room_rack_rate.room_type = room_type["room_type_object"]
        room_rack_rate.fill_room_rack_rate_id()
        room_rack_rates.append(room_rack_rate)
    return room_rack_rates


def create_property_bank_details(property_detail, property_launch):
    if property_launch.get("bank_details"):
        bank_details = property_launch["bank_details"]
        bank_details_object = BankDetail(
            account_name=bank_details["account_name"],
            account_number=bank_details["account_number"],
            ifsc_code=bank_details["ifsc_code"],
            bank=bank_details["bank"],
            branch=bank_details["branch"],
            property_detail=property_detail,
        )

        return bank_details_object

