import datetime
import logging

from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import Seller

logger = logging.getLogger(__name__)


class SellerRepository(BaseRepository):

    @staticmethod
    def get_sellers(property_id=None, seller_category_id=None):
        query = Seller.query
        if property_id:
            query = query.filter(Seller.property_id == property_id)
        if seller_category_id:
            query = query.filter(Seller.seller_category_id == seller_category_id)
        return query.all()

    @staticmethod
    def get_seller_base_currency(seller_id=None):
        if seller_id:
            seller = Seller.query.filter(Seller.seller_id == seller_id).first()
            if not seller:
                raise ValueError('seller with given seller_id not present')
            return seller.base_currency_code

    @staticmethod
    def get_seller_by_id(seller_id):
        query = Seller.query
        if seller_id:
            query = query.filter(Seller.seller_id == seller_id)
        return query.all()

    def does_seller_exist(self, seller_id):
        return self.session().query(Seller.query.filter(Seller.seller_id == seller_id).exists()).scalar()
     
    @staticmethod
    def get_seller(seller_id):
        seller = Seller.query.filter(Seller.seller_id == seller_id).first()
        if not seller:
            raise ValueError('seller with given seller_id not present')
        return seller

    @staticmethod
    def rollover_current_business_date_for_all_sellers(property_id, business_date=None):
        if not business_date:
            logger.info("Incrementing business date of sellers for property_id: %s by 1, setting to %s" % (
                property_id, Seller.current_business_date + datetime.timedelta(days=1)))
            Seller.query.filter(Seller.property_id == property_id).update(
                {Seller.current_business_date: Seller.current_business_date + datetime.timedelta(days=1)})
        else:
            logger.info("Updating business date of sellers for property_id: %s to %s" % (property_id, business_date))
            Seller.query.filter(Seller.property_id == property_id).update(
                {Seller.current_business_date: business_date})
        return Seller.query.filter(Seller.property_id == property_id).all()
