import itertools
import json
import logging
import os
from collections import defaultdict
from datetime import datetime
from treebo_commons.utils import dateutils
from flask.globals import current_app
from marshmallow import ValidationError
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.utils import dateutils

from cataloging_service.client.crs_client import CRSClient
from cataloging_service.constants import error_codes, model_choices, constants
from cataloging_service.constants.messaging_constants import PropertyMessageActions
from cataloging_service.constants.model_choices import PropertyChoices, StandardStatusChoices
from cataloging_service.domain.tenant_config_service import TenantConfigService
from cataloging_service.domain.dtos.sku_dto import SkuDto
from cataloging_service.domain.entities.property import PropertyEntity, PropertyImageEntity, \
    PropertyTransportStationEntity, PropertyLandmarkEntity, PropertyVideoEntity
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.extensions import cache
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.messaging.messaging_wrappers import PropertyMessagingWrapper, \
    RestaurantMessagingWrapper, BarMessagingWrapper, BanquetHallWrapper, PropertyAmenitiesWrapper, \
    AmenitySummaryWrapper, SellerWrapper, PropertySkuWrapper
from cataloging_service.infrastructure.repositories.pagination import Pagination
from cataloging_service.models import Location, PropertyDetail, Property, BankDetail, GuestFacingProcess, Owner, \
    Ownership, Landmark, Description, NeighbouringPlace, PropertyImage, AmenitySummary, Provider, SkuActivation, \
    PropertySku, properties_sku_categories, PropertyVideo, Brand
from cataloging_service.utils import Utils, group_list
from cataloging_service.constants.constants import (
    VIDEO_UPLOAD_FOLDER_NAME,
    ALLOWED_VIDEO_EXTENSIONS,
    DYNAMIC_PRICE_SKU_CATEGORY_TO_PREFIXES,
    DEFAULT_RACK_RATE_FOR_DYNAMIC_PRICING_SKUS,
    EXTRA_ADULT, COST_CENTER_CODE_MAPPING, DEFAULT_COST_CENTER_ID_BRAND_CODE,
)
from core.property.fuzzy_match import filter_fuzzy

logger = logging.getLogger(__name__)


class PropertyService:
    def __init__(self, property_repository, location_repository, file_repository, meta_repository, messaging_service,
                 file_upload_client, property_policy_map_repository, sku_repository, owner_repository,
                 ownership_repository, crs_client, seller_repository, room_rack_rate_repository):
        self.__property_repository = property_repository
        self.__location_repository = location_repository
        self.__file_repository = file_repository
        self.__meta_repository = meta_repository
        self.__file_upload_client = file_upload_client
        self.__messaging_service = messaging_service
        self.ownership_repository = ownership_repository
        self.owner_repository = owner_repository
        self.__property_policy_map_repository = property_policy_map_repository
        self.__sku_repository = sku_repository
        self.__crs_client = crs_client
        self.seller_repository = seller_repository
        self.room_rack_rate_repository = room_rack_rate_repository

    @atomic_operation
    def create_property(self, create_property_request):
        created_property = self._create_property_object(create_property_request)

        self._create_property_location(created_property, create_property_request.location)

        if create_property_request.property_details:
            property_detail = self._create_property_details(created_property, create_property_request.property_details)

            if create_property_request.property_details.bank_details:
                self._create_property_bank(property_detail, create_property_request.property_details.bank_details)

        if create_property_request.guest_facing_details:
            self._create_guest_facing_details(created_property, create_property_request.guest_facing_details)

        if create_property_request.owners:
            for owner in create_property_request.owners:
                self._create_property_owner(created_property, owner)

        if create_property_request.landmarks:
            for landmark in create_property_request.landmarks:
                self._create_landmark(created_property, landmark)

        if create_property_request.description:
            self._create_description(created_property, create_property_request.description)

        if create_property_request.neighbouring_places:
            self._create_neighbourhood(created_property, create_property_request.neighbouring_places)

        if create_property_request.suited_to:
            self._set_guest_types(created_property, create_property_request.suited_to)

        return created_property

    def can_property_be_signed(self, property_object):
        if property_object.status != model_choices.PropertyChoices.STATUS_NEAR_CONFIRMED:
            logger.error('Property is not in near confirmed state')
            return False
        if not property_object.property_detail:
            logger.error('Not signing property because property details are not present')
            return False
        if not property_object.property_detail.bank_detail:
            logger.error('Not signing property because bank details are not present')
            return False
        if not self.__property_repository.get_property_primary_owners(property_object.id):
            logger.error('Not signing property because there is no primary owner')
            return False

        file_types_uploaded = self.__file_repository.get_distinct_property_file_types(property_object.id)

        if len(constants.SIGN_FILES - file_types_uploaded) > 0:
            logger.error('All sign files not uploaded')
            return False

        return True

    @atomic_operation
    def sign_property(self, property_object):
        property_object.status = model_choices.PropertyChoices.STATUS_SIGNED
        property_object.signed_date = Utils.get_current_date_ist()
        property_object.current_business_date = datetime.date.today()
        self.__property_repository.persist(property_object)

        notification = self.__meta_repository.get_notificaion_object(constants.RECCE_NOTIFICATION_TYPE)
        receivers = notification.receivers.split(',')
        if receivers:
            Utils.send_recce_mail(property_object, receivers)

        notification = self.__meta_repository.get_notificaion_object(constants.CONTENT_TEAM)
        receivers = notification.receivers.split(',')
        if receivers:
            Utils.send_content_mail(property_object, receivers)

        self.notify_property_status_change(property_object)

        logger.info('Property Signed %s' % property_object)
        return property_object

    def _update_property_object(self, property_object, modify_property_request):
        if modify_property_request.name:
            property_object.old_name = modify_property_request.name.old_name
            property_object.name = modify_property_request.name.new_name
            property_object.legal_name = modify_property_request.name.legal_name
        property_object.hx_id = modify_property_request.hx_id

        return self.__property_repository.persist(property_object)

    @atomic_operation
    def update_property(self, property_object, modify_property_request):
        self._update_property_object(property_object, modify_property_request)

        if modify_property_request.location:
            if not property_object.location:
                self._create_property_location(property_object, modify_property_request.location)
            else:
                self._update_property_location(property_object.location, modify_property_request.location)

        if modify_property_request.property_details:
            if not property_object.property_detail:
                property_detail = self._create_property_details(property_object,
                                                                modify_property_request.property_details)
            else:
                property_detail = self._update_property_details(property_object.property_detail,
                                                                modify_property_request.property_details)
                cache.expire_cache(property_detail)

            if modify_property_request.property_details.bank_details:
                if not property_detail.bank_detail:
                    self._create_property_bank(property_detail, modify_property_request.property_details.bank_details)
                else:
                    self._update_property_bank(property_object.property_detail.bank_detail,
                                               modify_property_request.property_details.bank_details)

        if modify_property_request.guest_facing_details:
            if not property_object.guest_facing_process:
                self._create_guest_facing_details(property_object, modify_property_request.guest_facing_details)
            else:
                self._update_guest_facing_details(property_object.guest_facing_process,
                                                  modify_property_request.guest_facing_details)

        if modify_property_request.owners:
            for owner in modify_property_request.owners:
                self._create_property_owner(property_object, owner)

        if modify_property_request.landmarks:
            for landmark in modify_property_request.landmarks:
                self._create_landmark(property_object, landmark)

        if modify_property_request.suited_to:
            self._set_guest_types(property_object, modify_property_request.suited_to)

        if modify_property_request.description:
            if not property_object.description:
                self._create_description(property_object, modify_property_request.description)
            else:
                self._update_description(property_object.description, modify_property_request.description)

        if modify_property_request.neighbouring_places:
            if not property_object.neighbouring_place:
                self._create_neighbourhood(property_object, modify_property_request.neighbouring_places)
            else:
                self._update_neighbourhood(property_object.neighbouring_place,
                                           modify_property_request.neighbouring_places)

        return property_object

    def get_property(self, property_id):
        property = self.__property_repository.get_property(property_id)

        if not property:
            raise CatalogingServiceException(error_codes.PROPERTY_NOT_FOUND, context='property id: %s' % property_id,
                                             send_error_mail=False)

        return property

    def get_properties_in_bulk(self, property_ids):
        properties = []
        for property_id in property_ids:
            property = self.__property_repository.get_property(property_id)
            properties.append(property)

        return properties

    def get_property_by_hx_property_id(self, hx_property_id):
        property = self.__property_repository.get_property_by_hx_property_id(hx_property_id)

        if not property:
            raise CatalogingServiceException(error_codes.PROPERTY_NOT_FOUND,
                                             context='hx property id: %s' % hx_property_id,
                                             send_error_mail=False)

        return property

    def get_properties_by_ids(self, property_ids, fields=None):
        if fields:
            properties = self.__property_repository.get_properties_by_ids_with_selective_joinedload(property_ids,
                                                                                                    fields=fields)
        else:
            properties = self.__property_repository.get_properties_by_ids(property_ids)

        if not fields or 'suited_to' in fields:
            guest_type_grouped_by_id = {guest_type.id: guest_type for guest_type in
                                        self.__meta_repository.get_all_guest_types()}

            grouped_guest_types = defaultdict(list)
            for guest_type_property in self.__property_repository.get_guest_types_for_properties(property_ids):
                grouped_guest_types[guest_type_property.property_id].append(
                    guest_type_grouped_by_id[guest_type_property.guest_type_id])
        else:
            grouped_guest_types = dict()

        grouped_property_landmarks = defaultdict(list)
        if not fields or 'landmarks' in fields:
            property_landmarks = self.__property_repository.get_property_landmarks(property_ids)
            for prop_landmark in property_landmarks:
                grouped_property_landmarks[prop_landmark.property_id].append(
                    PropertyLandmarkEntity(landmark=prop_landmark.landmark, property_landmark=prop_landmark))

        grouped_property_transport_stations = defaultdict(list)
        if not fields or 'transport_stations' in fields:
            property_transport_stations = self.__property_repository.get_property_transport_stations(property_ids)
            for station in property_transport_stations:
                property_transport_station = PropertyTransportStationEntity(
                    station.hatchback_cab_fare, station.property_direction, station.distance_from_property,
                    station.sedan_cab_fare, station.transport_station)
                grouped_property_transport_stations[station.property_id].append(property_transport_station)

        if not fields or 'room_count' in fields:
            property_room_count = self.__property_repository.get_property_room_count(property_ids)
        else:
            property_room_count = dict()

        grouped_room_type_configs = dict()
        room_type_config_room_count = dict()

        room_type_configs = self.__property_repository.get_room_type_configs(property_ids)
        room_type_config_group_by_id = {room_type_config.id: room_type_config for room_type_config in
                                        room_type_configs}
        if not fields or 'room_type_configs' in fields:
            grouped_room_type_configs = group_list(room_type_configs, 'property_id')

            if grouped_room_type_configs:
                room_type_config_room_count = self.__property_repository.get_room_type_config_room_count(
                    room_type_config_group_by_id.keys())

        if not fields or 'property_images' in fields:
            property_images = self.__property_repository.get_properties_images(property_ids)
            grouped_property_images = defaultdict(list)
            for image in property_images:
                grouped_property_images[image.property_id].append(
                    PropertyImageEntity(image, room_type_config_group_by_id.get(
                        image.room_type_config_id) if image.room_type_config_id else None))
        else:
            grouped_property_images = dict()

        if not fields or 'property_videos' in fields:
            property_videos = self.__property_repository.get_properties_videos(property_ids)
            grouped_property_videos = defaultdict(list)
            for video in property_videos:
                grouped_property_videos[video.property_id].append(
                    PropertyVideoEntity(video))
        else:
            grouped_property_videos = dict()

        if not fields or 'skus' in fields:
            property_skus = self.__property_repository.get_property_skus(property_ids)
            grouped_property_skus = group_list(property_skus, 'property_id')
            associated_modular_skus_for_all_room_types = self.__sku_repository.get_modular_skus_for_category('stay')
            grouped_associated_modular_skus = {sku.name.lower(): sku for sku in
                                               associated_modular_skus_for_all_room_types}
        else:
            grouped_property_skus = dict()
            grouped_associated_modular_skus = dict()

        property_entities = [
            PropertyEntity(
                property=hotel,
                guest_types=grouped_guest_types.get(hotel.id),
                location=hotel.location if not fields or 'location' in fields else None,
                property_details=hotel.property_detail if not fields or 'property_details' in fields else None,
                guest_facing_process=hotel.guest_facing_process,
                ownerships=hotel.ownerships if not fields or 'owners' in fields else None,
                property_landmarks=grouped_property_landmarks.get(hotel.id),
                description=hotel.description if not fields or 'description' in fields else None,
                neighbouring_place=hotel.neighbouring_place if not fields or 'neighbouring_places' in fields else None,
                transport_station_assocs=grouped_property_transport_stations.get(hotel.id),
                sku_categories=hotel.sku_categories if not fields or 'sku_categories' in fields else None,
                room_count=property_room_count.get(hotel.id),
                property_images=grouped_property_images.get(hotel.id),
                property_videos=grouped_property_videos.get(hotel.id),
                amenity_summary=hotel.amenity_summary if not fields or 'amenity_summary' in fields else None,
                room_type_configurations=grouped_room_type_configs.get(hotel.id),
                property_skus=grouped_property_skus.get(hotel.id),
                brands=hotel.brands if not fields or 'brands' in fields else None,
                room_type_config_room_count=room_type_config_room_count,
                associated_modular_skus_for_all_room_types=grouped_associated_modular_skus,
                logo=hotel.logo,
                timezone=hotel.timezone,
                base_currency_code=hotel.base_currency_code,
                country_code=hotel.country_code,
                cost_center_id=hotel.cost_center_id,
                region=hotel.region,
            )
            for hotel in properties]

        return property_entities

    def get_all_properties(self, ids=None):
        return self.__property_repository.get_all_properties(ids)

    def get_all_properties_by_status_and_given_ids_and_pagination_and_sold_as(self, statuses, page, items_per_page,
                                                                              ids, sold_as):
        return self.__property_repository.rget_all_properties_by_status_and_ids_and_sold_as(statuses, page,
                                                                                            items_per_page,
                                                                                            ids, sold_as)

    def sget_all_live_properties(self, ids=None):
        return self.__property_repository.rget_all_live_properties(ids)

    def _create_description(self, property_object, create_description_request):
        description_object = Description()
        description_object.property_id = property_object.id
        description_object.property_description = create_description_request.property_description
        description_object.acacia_description = create_description_request.acacia_description
        description_object.oak_description = create_description_request.oak_description
        description_object.maple_description = create_description_request.maple_description
        description_object.mahogany_description = create_description_request.mahogany_description
        description_object.trilight_one = create_description_request.trilight_one
        description_object.trilight_two = create_description_request.trilight_two
        description_object.trilight_three = create_description_request.trilight_three
        logger.debug('Created Description %s' % description_object)
        return self.__property_repository.persist(description_object)

    def _create_property_object(self, create_property_request):
        property_object = Property()
        property_object.old_name = create_property_request.name.old_name
        property_object.name = create_property_request.name.new_name
        property_object.legal_name = create_property_request.name.legal_name
        property_object.hx_id = create_property_request.hx_id
        property_object.current_business_date = dateutils.current_date()

        city = self.__location_repository.get_city(create_property_request.location.city_id)
        if not city:
            raise CatalogingServiceException(error_codes.CITY_NOT_FOUND)

        property_object = self.assign_id_and_save_property(property_object, city)

        logger.debug('Created Property %s' % property_object)

        return property_object

    def assign_id_and_save_property(self, property_object, city, brand_code=None):
        for retry_count in range(constants.MAX_PROPERTY_ID_RETRY):
            property_object.id = Utils.generate_property_id(city, property_object.old_name)
            property_object.hx_id = property_object.id if not property_object.hx_id else property_object.hx_id
            if brand_code:
                property_object.cost_center_id = Utils.generate_cost_center_id(property_object.id,
                                                                               brand_code,
                                                                               property_object.region,
                                                                               city.name)
            existing_property = self.__property_repository.get_property(property_object.id)

            if existing_property:
                logger.error('Received integrity error while saving property, %s' % property_object)
                if retry_count + 1 == constants.MAX_PROPERTY_ID_RETRY:
                    raise CatalogingServiceException(error_codes.PROPERTY_ID_CLASHED)
            else:
                return self.__property_repository.persist(property_object)

    def _update_description(self, property_description_object, update_description_request):
        property_description_object.property_description = update_description_request.property_description
        property_description_object.acacia_description = update_description_request.acacia_description
        property_description_object.oak_description = update_description_request.oak_description
        property_description_object.maple_description = update_description_request.maple_description
        property_description_object.mahogany_description = update_description_request.mahogany_description
        property_description_object.trilight_one = update_description_request.trilight_one
        property_description_object.trilight_two = update_description_request.trilight_two
        property_description_object.trilight_three = update_description_request.trilight_three

        return self.__property_repository.persist(property_description_object)

    def _create_neighbourhood(self, property_object, create_nearby_request):
        neighbourhood_object = NeighbouringPlace()
        neighbourhood_object.property_id = property_object.id
        neighbourhood_object.nearest_hospital = create_nearby_request.nearest_hospital
        neighbourhood_object.utility_shops = create_nearby_request.utility_shops
        neighbourhood_object.restaurants = create_nearby_request.restaurants
        neighbourhood_object.tourist_spots = create_nearby_request.tourist_spots
        neighbourhood_object.corporate_offices = create_nearby_request.corporate_offices
        neighbourhood_object.popular_malls = create_nearby_request.popular_malls
        neighbourhood_object.shopping_streets = create_nearby_request.shopping_streets
        neighbourhood_object.city_centre = create_nearby_request.city_centre
        logger.debug('Created Neighbourhood %s' % neighbourhood_object)
        return self.__property_repository.persist(neighbourhood_object)

    def _update_neighbourhood(self, neighbourhood_object, update_neighbourhood_request):
        neighbourhood_object.nearest_hospital = update_neighbourhood_request.nearest_hospital
        neighbourhood_object.utility_shops = update_neighbourhood_request.utility_shops
        neighbourhood_object.restaurants = update_neighbourhood_request.restaurants
        neighbourhood_object.tourist_spots = update_neighbourhood_request.tourist_spots
        neighbourhood_object.corporate_offices = update_neighbourhood_request.corporate_offices
        neighbourhood_object.popular_malls = update_neighbourhood_request.popular_malls
        neighbourhood_object.shopping_streets = update_neighbourhood_request.shopping_streets
        neighbourhood_object.city_centre = update_neighbourhood_request.city_centre

        return self.__property_repository.persist(neighbourhood_object)

    def _create_property_location(self, property_object, create_location_request):
        location_object = Location()
        location_object.property_id = property_object.id
        location_object.city_id = create_location_request.city_id
        location_object.latitude = create_location_request.latitude
        location_object.longitude = create_location_request.longitude
        location_object.pincode = create_location_request.pincode
        location_object.postal_address = create_location_request.postal_address
        location_object.maps_link = create_location_request.maps_link
        location_object.micro_market_id = create_location_request.micro_market_id
        location_object.locality_id = create_location_request.locality_id

        logger.debug('Created Property Location %s' % location_object)

        return self.__property_repository.persist(location_object)

    def _update_property_location(self, location_object, update_location_request):
        location_object.city_id = update_location_request.city_id
        location_object.latitude = update_location_request.latitude
        location_object.longitude = update_location_request.longitude
        location_object.pincode = update_location_request.pincode
        location_object.postal_address = update_location_request.postal_address
        location_object.maps_link = update_location_request.maps_link
        location_object.locality_id = update_location_request.locality_id
        location_object.micro_market_id = update_location_request.micro_market_id

        return self.__property_repository.persist(location_object)

    def _create_property_bank(self, property_detail_object, create_bank_request):
        bank_detail_object = BankDetail()

        bank_detail_object.account_name = create_bank_request.account_name
        bank_detail_object.account_number = create_bank_request.account_number
        bank_detail_object.ifsc_code = create_bank_request.ifsc_code
        bank_detail_object.bank = create_bank_request.bank
        bank_detail_object.branch = create_bank_request.branch
        bank_detail_object.account_type = create_bank_request.type
        bank_detail_object.property_detail = property_detail_object
        logger.debug('Created Property Bank %s' % bank_detail_object)
        return self.__property_repository.persist(bank_detail_object)

    def _update_property_bank(self, bank_detail_object, update_bank_request):
        bank_detail_object.account_name = update_bank_request.account_name
        bank_detail_object.account_number = update_bank_request.account_number
        bank_detail_object.account_type = update_bank_request.type
        bank_detail_object.bank = update_bank_request.bank
        bank_detail_object.branch = update_bank_request.branch
        bank_detail_object.ifsc_code = update_bank_request.ifsc_code

        return self.__property_repository.persist(bank_detail_object)

    def _create_property_details(self, property_object, create_details_request):
        property_detail_object = PropertyDetail()
        property_detail_object.property_id = property_object.id
        property_detail_object.neighbourhood_type = create_details_request.neighbourhood_type
        property_detail_object.neighbourhood_detail = create_details_request.neighbourhood_detail
        property_detail_object.property_type = create_details_request.property_type
        property_detail_object.property_style = create_details_request.style
        property_detail_object.style_detail = create_details_request.style_detail
        property_detail_object.construction_year = create_details_request.construction_year
        property_detail_object.building_style = create_details_request.building_type
        property_detail_object.floor_count = create_details_request.floor_count
        property_detail_object.star_rating = create_details_request.star_rating
        property_detail_object.previous_franchise = create_details_request.previously_different_franchise
        property_detail_object.reception_landline = create_details_request.reception_landline
        property_detail_object.reception_mobile = create_details_request.reception_mobile
        logger.debug('Created Property Details %s' % property_detail_object)
        return self.__property_repository.persist(property_detail_object)

    def _update_property_details(self, property_detail_object, update_details_request):
        property_detail_object.neighbourhood_type = update_details_request.neighbourhood_type
        property_detail_object.neighbourhood_detail = update_details_request.neighbourhood_detail
        property_detail_object.is_hotel_superhero_setup_completed = \
            update_details_request.is_hotel_superhero_setup_completed
        property_detail_object.property_type = update_details_request.property_type
        property_detail_object.property_style = update_details_request.style
        property_detail_object.style_detail = update_details_request.style_detail
        property_detail_object.construction_year = update_details_request.construction_year
        property_detail_object.building_style = update_details_request.building_type
        property_detail_object.floor_count = update_details_request.floor_count
        property_detail_object.star_rating = update_details_request.star_rating
        property_detail_object.previous_franchise = update_details_request.previously_different_franchise
        property_detail_object.reception_landline = update_details_request.reception_landline
        property_detail_object.reception_mobile = update_details_request.reception_mobile

        return self.__property_repository.persist(property_detail_object)

    def _create_guest_facing_details(self, property_object, create_guest_facing_details_request):
        guest_facing_process_object = GuestFacingProcess()
        guest_facing_process_object.property_id = property_object.id
        guest_facing_process_object.checkin_time = create_guest_facing_details_request.checkin_time
        guest_facing_process_object.checkout_time = create_guest_facing_details_request.checkout_time
        guest_facing_process_object.free_early_checkin = create_guest_facing_details_request.free_early_checkin_time
        guest_facing_process_object.free_late_checkout = create_guest_facing_details_request.free_late_checkout_time
        guest_facing_process_object.early_checkin_fee = create_guest_facing_details_request.early_checkin_fee
        guest_facing_process_object.late_checkout_fee = create_guest_facing_details_request.late_checkout_fee
        guest_facing_process_object.system_freeze_time = create_guest_facing_details_request.system_freeze_time
        logger.debug('Created Property Guest Facing Details %s' % guest_facing_process_object)

        return self.__property_repository.persist(guest_facing_process_object)

    @staticmethod
    def validate_and_sanitize_property_dates(property_object):
        if property_object.status in [PropertyChoices.STATUS_SIGNED, PropertyChoices.STATUS_DROPPED]:
            PropertyService.validate_signed_status_dates(property_object)
            property_object.churned_date = None

        if property_object.status in [PropertyChoices.STATUS_NEAR_CONFIRMED, PropertyChoices.STATUS_NOT_SIGNING]:
            property_object.launched_date = None
            property_object.churned_date = None
            property_object.signed_date = None

        if property_object.status == PropertyChoices.STATUS_LIVE:
            PropertyService.validate_launch_status_dates(property_object)

            property_object.churned_date = None

        if property_object.status == PropertyChoices.STATUS_CHURNED:
            PropertyService.validate_churned_dates(property_object)

    @staticmethod
    def validate_signed_status_dates(property_object):
        if not property_object.signed_date:
            raise CatalogingServiceException(error_codes.SIGNED_DATE_REQUIRED)

    @staticmethod
    def validate_launch_status_dates(property_object):
        PropertyService.validate_signed_status_dates(property_object)

        if not property_object.contractual_launch_date:
            raise CatalogingServiceException(error_codes.CONTRACTUAL_LAUNCH_DATE_REQUIRED)

        if not property_object.launched_date:
            raise CatalogingServiceException(error_codes.LAUNCH_DATE_REQUIRED)

        if property_object.signed_date > property_object.launched_date:
            raise CatalogingServiceException(error_codes.INVALID_DATE_ORDER)

    @staticmethod
    def validate_churned_dates(property_object):
        PropertyService.validate_launch_status_dates(property_object)

        if not property_object.churned_date:
            raise CatalogingServiceException(error_codes.CHURNED_DATE_REQUIRED)

        if property_object.launched_date > property_object.churned_date:
            raise CatalogingServiceException(error_codes.INVALID_DATE_ORDER)

        if property_object.churned_date > datetime.date(datetime.now()):
            raise CatalogingServiceException(error_codes.INVALID_CHURNED_DATE)

    def _update_guest_facing_details(self, guest_facing_detail_object, update_guest_facing_details_request):
        guest_facing_detail_object.checkin_time = update_guest_facing_details_request.checkin_time
        guest_facing_detail_object.checkout_time = update_guest_facing_details_request.checkout_time
        guest_facing_detail_object.free_early_checkin = update_guest_facing_details_request.free_early_checkin_time
        guest_facing_detail_object.free_late_checkout = update_guest_facing_details_request.free_late_checkout_time
        guest_facing_detail_object.early_checkin_fee = update_guest_facing_details_request.early_checkin_fee
        guest_facing_detail_object.late_checkout_fee = update_guest_facing_details_request.late_checkout_fee

        return self.__property_repository.persist(guest_facing_detail_object)

    def _create_property_owner(self, property_object, create_owner_request):
        if create_owner_request.is_primary_owner:
            if not create_owner_request.email:
                raise CatalogingServiceException(error_codes.EMAIL_REQUIRED_FOR_PRIMARY_OWNER)
            if not create_owner_request.phone_number:
                raise CatalogingServiceException(error_codes.PHONE_REQUIRED_FOR_PRIMARY_OWNER)

        owner_object = Owner()
        owner_object.first_name = create_owner_request.first_name
        owner_object.middle_name = create_owner_request.middle_name
        owner_object.last_name = create_owner_request.last_name
        owner_object.gender = create_owner_request.gender
        owner_object.email = create_owner_request.email
        owner_object.phone_number = create_owner_request.phone_number
        owner_object.date_of_birth = create_owner_request.dob
        owner_object.occupation = create_owner_request.occupation
        owner_object.education = create_owner_request.education

        logger.debug('Created Owner %s' % owner_object)
        owner_object = self.__property_repository.persist(owner_object)

        ownership_object = Ownership()
        ownership_object.owner_id = owner_object.id
        ownership_object.property_id = property_object.id
        ownership_object.primary = create_owner_request.is_primary_owner
        logger.debug('Created Ownership %s' % ownership_object)

        self.__property_repository.persist(ownership_object)

        return owner_object, ownership_object

    def _create_landmark(self, property_object, create_landmark_request):
        landmark_object = Landmark()
        landmark_object.property_id = property_object.id
        landmark_object.type = create_landmark_request.type
        landmark_object.name = create_landmark_request.name
        landmark_object.latitude = create_landmark_request.latitude
        landmark_object.longitude = create_landmark_request.longitude
        landmark_object.distance_from_property = create_landmark_request.hotel_distance
        landmark_object.property_direction = create_landmark_request.hotel_direction
        landmark_object.hatchback_cab_fare = create_landmark_request.hatchback_cab_fare
        landmark_object.sedan_cab_fare = create_landmark_request.sedan_cab_fare
        logger.debug('Created Landmark %s' % landmark_object)
        return self.__location_repository.persist(landmark_object)

    def _set_guest_types(self, property_object, suited_to):
        guest_types = self.__property_repository.get_guest_types(suited_to)
        property_object.guest_types.extend(guest_types)
        logger.debug('Associated Guest Types %s' % guest_types)
        return guest_types

    def _check_rack_rate_configuration(self, property_object):
        room_type_configs = self.__property_repository.get_property_room_type_configurations(
            property_object.id)
        room_type_id_to_adult_count_map = {room_type_config.room_type_id:
                                               room_type_config.adults
                                           for room_type_config in room_type_configs}
        room_type_id_to_name_map = {room_type_config.room_type_id: room_type_config.display_name
                                    for room_type_config in room_type_configs}
        room_type_rack_rate_count_details = \
            self.room_rack_rate_repository.get_room_rack_rates_count(
                property_object.id)
        room_type_id_to_rack_rate_count_map = {detail[0]: detail[1] for detail in
                                               room_type_rack_rate_count_details}

        error_message = ""
        if not room_type_id_to_rack_rate_count_map:
            error_message = 'For hotel: {property_id}, rack rates are not configured '.format(
                property_id=property_object.id)
        else:
            for room_type_id, adult_count in room_type_id_to_adult_count_map.items():
                if (not room_type_id_to_rack_rate_count_map.get(room_type_id)) or \
                        (adult_count != room_type_id_to_rack_rate_count_map.get(room_type_id)):
                    error_message = 'For hotel: {property_id}, rack rates are not configured ' \
                                    'for configuration: {room_type} - {adult_count}'.format(
                        property_id=property_object.id, room_type=room_type_id_to_name_map.get(
                            room_type_id), adult_count=adult_count)
                    break

        if error_message:
            return False, error_message
        return True, ""

    def is_property_launchable(self, property_object, tenant_id):
        # status, reason = self._is_property_object_complete(property_object)
        # if not status:
        #     return status, reason
        #
        # status, reason = self._is_property_detail_complete(property_object.property_detail)
        # if not status:
        #     return status, reason
        #
        # status, reason = self._is_property_location_complete(property_object.location)
        # if not status:
        #     return status, reason
        #
        # status, reason = self._is_property_room_information_complete(property_object)
        # if not status:
        #     return status, reason
        #
        # status, reason = self._is_guest_facing_details_complete(property_object.guest_facing_process)
        # if not status:
        #     return status, reason
        #
        # status, reason = self._is_guest_type_complete(property_object.guest_types)
        # if not status:
        #     return status, reason
        #
        # status, reason = self._is_property_description_complete(property_object.description)
        # if not status:
        #     return status, reason
        #
        # status, reason = self._is_property_neighbourhood_complete(property_object.neighbouring_place)
        # if not status:
        #     return status, reason
        #
        # status, reason = self._are_launch_files_uploaded(property_object)
        # if not status:
        #     return status, reason
        #
        # status, reason = self._is_sku_created(property_object)
        # if not status:
        #     return status, reason
        if tenant_id == constants.TREEBO_TENANT:
            are_rack_rates_configured, error_message = \
                self._check_rack_rate_configuration(property_object)
            if not are_rack_rates_configured:
                return False, error_message
            error_message = ''
            if not property_object.property_detail.pan:
                error_message += 'pan '
            if not property_object.property_detail.gstin:
                error_message += 'gstin '
            if not property_object.location.legal_address:
                error_message += 'legal address '
            if not property_object.property_detail.sold_as:
                error_message += 'sold_as'
            if error_message:
                return False, error_message + 'information incomplete'
        else:
            if not property_object.location.legal_address:
                return False, 'legal_address information incomplete'

        return True, ''

    def _is_property_object_complete(self, property):
        status = (property.name is not None) and (
                property.hx_id is not None) and (property.old_name is not None) and (
                         property.legal_name is not None) and (
                         property.signed_date is not None) and (property.contractual_launch_date is not None) and (
                     (property.launched_date is not None))

        if not status:
            return status, 'Property information incomplete'
        return status, ''

    def _is_property_detail_complete(self, property_detail):
        status = (property_detail.construction_year is not None) and (property_detail.floor_count is not None) and (
                property_detail.star_rating is not None) and (property_detail.reception_landline is not None) and (
                         property_detail.reception_mobile is not None) and (
                         property_detail.neighbourhood_type is not None) and (
                         property_detail.property_type is not None) and (
                         property_detail.property_style is not None) and (property_detail.building_style is not None)

        if not status:
            return status, 'Property details incomplete'
        return status, ''

    def _is_property_location_complete(self, property_location):
        status = (property_location.latitude is not None) and (property_location.longitude is not None) and (
                property_location.pincode is not None) and (property_location.postal_address is not None) and (
                         property_location.maps_link is not None) and (property_location.micro_market is not None) and (
                         property_location.locality is not None)

        if not status:
            return status, 'Location details incomplete'
        return status, ''

    def _is_property_room_information_complete(self, property):

        room_types = set()

        total_rooms = 0
        for room in property.rooms:
            total_rooms += 1
            if (room.building_number is None) or (room.floor_number is None) or (
                    room.size is None):
                return False, 'Room details of %s incomplete' % room.room_number
            room_types.add(room.room_type)

        if total_rooms < constants.MIN_ROOM_COUNT:
            return False, 'Minimum %s rooms are needed to launch property' % constants.MIN_ROOM_COUNT

        room_config_room_types = set()
        for room_config in property.room_type_configurations:
            room_config_room_types.add(room_config.room_type)
            if (room_config.max_occupancy is None) or (room_config.adults is None) or (room_config.mm_id is None) or (
                    room_config.children is None) or (room_config.max_total is None):
                return False, 'Room config incomplete'

            if (room_config.adults + room_config.children) < room_config.max_total:
                return False, 'Max total should be <= adults + children'

        if len(room_types - room_config_room_types) > 0:
            return False, 'Room config missing'

        return True, ''

    def _is_guest_facing_details_complete(self, guest_facing_details):
        status = guest_facing_details is not None

        if not status:
            return status, 'Guest facing details incomplete'
        return status, ''

    def _is_guest_type_complete(self, property_guest_types):
        status = property_guest_types is not None and (len(property_guest_types) > 0)
        if not status:
            return status, 'Guest types not added'
        return status, ''

    def _is_property_description_complete(self, description):
        status = (description is not None) and (description.property_description is not None) and (
                description.acacia_description is not None) and (description.oak_description is not None) and (
                         description.maple_description is not None) and (
                         description.mahogany_description is not None) and (
                         description.trilight_one is not None) and (description.trilight_two is not None) and (
                         description.trilight_three is not None)

        if not status:
            return status, 'Description is incomplete'
        return status, ''

    def _is_property_neighbourhood_complete(self, neighbourhood):
        status = (neighbourhood is not None) and (neighbourhood.nearest_hospital is not None) and (
                neighbourhood.utility_shops is not None) and (neighbourhood.restaurants is not None) and (
                         neighbourhood.tourist_spots is not None) and (
                         neighbourhood.corporate_offices is not None) and (
                         neighbourhood.popular_malls is not None) and (neighbourhood.shopping_streets is not None) and (
                         neighbourhood.city_centre is not None)

        if not status:
            return status, 'Neighbourhood details incomplete'
        return status, ''

    def _are_launch_files_uploaded(self, property):
        launch_file_types = set()
        launch_file_types.update(constants.LAUNCH_FILES)
        if property.property_detail.previous_franchise:
            launch_file_types.add(model_choices.GoogleDriveFileTypeChoices.FILE_TYPE_OLD_FRANCHISE_TERMINATION_DOCUMENT)

        uploaded_file_types = self.__file_repository.get_distinct_property_file_types(property.id)
        difference_set = launch_file_types - uploaded_file_types

        if len(difference_set) > 0:
            return False, 'Add the files:%s' % difference_set

        return True, ''

    def _is_sku_created(self, property_object):
        bundled_skus = [sku for sku in property_object.sku_s if sku.is_modular == 'FALSE']
        if not bundled_skus:
            return False, 'Skus for property undefined'
        room_type_configurations = property_object.room_type_configurations
        total_config = 0
        for room_type_configuration in room_type_configurations:
            total_config = total_config + getattr(room_type_configuration, 'max_total', 0)
        if len(bundled_skus) >= total_config > 0:
            return True, ''
        else:
            return False, 'Skus for property undefined'

    def notify_property_status_change(self, property_object):
        notification = self.__meta_repository.get_notificaion_object(constants.BUSINESS_STAKE_HOLDER_TYPE)
        receivers = notification.receivers.split(',')
        if receivers:
            Utils.send_property_status_change_mail(property_object, receivers)

    @atomic_operation
    def upload_property_images(self, property_id, file):
        directory = current_app.config[constants.ZIP_DIRECTORY] + property_id + '_' + str(Utils.get_epoch_time())
        Utils.extract_zip(file, directory)
        logger.info('Extracting files to %s' % directory)
        property_object = self.get_property(property_id)
        room_type_configs = self.__property_repository.get_property_room_type_configurations(property_object.id)
        room_type_map = {room_type_config.room_type.type.lower(): room_type_config for room_type_config in
                         room_type_configs}
        room_type_map['common'] = None
        applicable_room_types = room_type_map.keys()
        root, folders, images = next(os.walk(directory))
        logger.info('Image folders are %s' % folders)
        folder_map = {folder: room_type_map[folder.lower()] for folder in folders if
                      folder.lower() in applicable_room_types}
        models = []
        sort_order = 0

        for folder_name, room_type_config in folder_map.items():
            path, folders, files = next(os.walk(directory + '/' + folder_name))
            logger.info('Files are %s' % files)
            room_type = room_type_config.room_type.type if room_type_config else 'common'
            files = [(path, file) for file in files if
                     file.lower().endswith('jpeg') or file.lower().endswith('png') or file.lower().endswith('jpg')]
            if not property_object.name:
                raise Exception("Property name empty")
            # re.sub(r'[\s*]', '_', property_object.name)
            uploaded_files = self.__file_upload_client.upload_files(
                current_app.config[constants.IMAGE_BUCKET_NAME], files,
                property_object.name.replace(" ", "_") + '/' + room_type)
            for s3_file in uploaded_files:
                models.append(
                    PropertyImage(path=s3_file, property_id=property_object.id, room_type_config=room_type_config,
                                  sort_order=sort_order))
                sort_order += 1
        Utils.delete_local_folder(directory)

        return self.__property_repository.persist_all(models)

    def upload_image(self, file, directory='.'):
        """
        Made the path simpler and straight forward, becomes easy to upload manually
        :param file:
        :param directory:
        :return:
        """
        try:
            s3_path = 'reseller'

            files = [(directory, file.filename)]
            uploaded_files = self.__file_upload_client.upload_files(current_app.config[constants.IMAGE_BUCKET_NAME],
                                                                    files, s3_path)

            os.remove(directory + '/' + file.filename)
            if uploaded_files:
                return uploaded_files[0]
            return False

        except Exception as e:
            logger.exception(e)
            return False

    def get_property_images(self, property_object):
        return self.__property_repository.get_property_images(property_object.id)

    def upload_property_videos_on_s3(self, property, file):
        directory = '{0}{1}_{2}'.format(current_app.config[constants.ZIP_DIRECTORY], property.id,
                                        str(Utils.get_epoch_time()))
        Utils.extract_zip(file, directory)
        logger.info('Extracting files to %s' % directory)

        folder_path = '{0}/{1}'.format(directory, VIDEO_UPLOAD_FOLDER_NAME)
        if not os.path.exists(folder_path):
            raise Exception("Folder Name should be Common")

        path, folders, files = next(os.walk(folder_path))
        logger.info('Files are %s' % files)
        files = [(path, file) for file in files if file.lower().endswith(ALLOWED_VIDEO_EXTENSIONS)]
        if not files:
            raise Exception("Either folder is empty or given file doesn't have valid file extension.")
        if not property.name:
            raise Exception("Property name empty")
        uploaded_files = self.__file_upload_client.upload_files(
            current_app.config[constants.IMAGE_BUCKET_NAME], files,
            '/{0}/{1}'.format(property.name.replace(" ", "_"), VIDEO_UPLOAD_FOLDER_NAME))
        Utils.delete_local_folder(directory)
        return uploaded_files

    @atomic_operation
    def save_property_videos(self, property_videos):
        return self.__property_repository.persist_all(property_videos)

    @atomic_operation
    def update_property_video(self, property_video):
        self.__property_repository._update(property_video)

    @atomic_operation
    def delete_property_video(self, property_video):
        self.__property_repository.delete(property_video)

    def get_property_videos(self, property_object):
        return self.__property_repository.get_property_videos(property_object.id)

    def publish_properties(self, ids, action):
        properties = self.__property_repository.get_all_properties(ids)
        messages = [PropertyMessagingWrapper(property, False, False, action).get_json() for property in properties]
        for message in messages:
            self.__messaging_service.publish_property_message(message)

    def publish_live_properties(self, ids, **kwargs):
        properties = self.__property_repository.get_all_properties(ids)
        messages = [PropertyMessagingWrapper(property, False, False,
                                             action=kwargs["action"]).get_json() for property in properties]
        for message in messages:
            message["data"]["property_live"] = kwargs["is_live"]
            self.__messaging_service.publish_property_message(message)

    def publish_restaurants(self, ids):
        restaurants = self.__property_repository.get_all_restaurants(ids)
        messages = [RestaurantMessagingWrapper(restaurant, False, False).get_json() for restaurant in restaurants]
        for message in messages:
            self.__messaging_service.publish_property_amenity_message(message)

    def publish_bars(self, ids):
        bars = self.__property_repository.get_all_bars(ids)
        messages = [BarMessagingWrapper(bar, False, False).get_json() for bar in bars]
        for message in messages:
            self.__messaging_service.publish_property_amenity_message(message)

    def publish_banquet_halls(self, ids):
        banquet_halls = self.__property_repository.get_all_banquet_halls(ids)
        messages = [BanquetHallWrapper(hall, False, False).get_json() for hall in banquet_halls]
        for message in messages:
            self.__messaging_service.publish_property_amenity_message(message)

    def publish_amenity_summaries(self, ids):
        amenity_summaries = self.__property_repository.get_all_amenity_summaries(ids)
        messages = [AmenitySummaryWrapper(summary, False, False).get_json() for summary in amenity_summaries]
        for message in messages:
            self.__messaging_service.publish_amenity_summary_message(message)

    def publish_property_amenities(self, ids):
        amenities = self.__property_repository.get_all_property_amenities(ids)
        messages = [PropertyAmenitiesWrapper(amenity, False, False).get_json() for amenity in amenities]
        for message in messages:
            self.__messaging_service.publish_property_amenity_message(message)

    def get_facility_category_map(self):
        mappings = self.__property_repository.get_facility_mappings()

        return {mapping.facility_name: mapping.category.category for mapping in mappings}

    def set_amenity_summary(self, property_object):
        summary = self.__property_repository.get_property_amenity_summary(property_object.id)
        facility_category_map = self.get_facility_category_map()
        amenity_map = dict(property_amenities=[])
        property_amenities = self._get_property_amenity_summary(property_object)

        for amenity in property_amenities:
            amenity_map['property_amenities'].append(
                dict(amenity_key=amenity, category=facility_category_map.get(amenity, '')))

        room_amenities = self._get_room_amenity_summary(property_object)

        for room_type, amenities in room_amenities.items():
            amenity_map[room_type] = [dict(amenity_key=amenity, category=facility_category_map.get(amenity, '')) for
                                      amenity in amenities]

        if not summary:
            summary = AmenitySummary()
            summary.property_id = property_object.id

        summary.summary = json.dumps(amenity_map)

        self.__property_repository.persist(summary)

    def _get_property_amenity_summary(self, property_object):
        amenities = set()
        if property_object.property_amenity:
            amenities = property_object.property_amenity.get_applicable_amenities()

        if property_object.bars:
            amenities.add('bar')
        if property_object.restaurants:
            amenities.add('restaurant')
        if property_object.banquet_halls:
            amenities.add('banquet_hall')

        return amenities

    def _get_room_amenity_summary(self, property_object):
        amenity_map = dict()

        for room in property_object.rooms:
            if not room.is_active:
                continue

            room_type = room.room_type.type
            room_amenities = room.amenity.get_applicable_amenities() if room.amenity else set()
            if room_type not in amenity_map:
                amenity_map[room_type] = room_amenities

            amenity_map[room_type].intersection_update(room_amenities)

        return amenity_map

    def is_duplicate_landmark(self, name, latitude, longitude):
        possible_duplicates = self.__property_repository.get_landmarks(latitude=latitude, longitude=longitude)
        for duplicate in possible_duplicates:
            landmark_name = Utils.strip_extra_whitespace(name).lower()
            duplicate_name = Utils.strip_extra_whitespace(duplicate.name).lower()
            if landmark_name == duplicate_name:
                return True

        return False

    @atomic_operation
    def set_amenity_summaries(self):
        properties = self.get_all_properties()
        for property in properties:
            self.set_amenity_summary(property)

    def get_sku_categories(self, id):
        property = self.get_property(id)
        return property.sku_categories

    def get_properties_by_provider_and_photel_codes(self, provider, phtl_ids=None):
        """
        :param provider:
        :param phtl_ids: provider hotel codes
        :return:
        """
        if type(provider) is Provider and provider.id:
            return self.__property_repository.get_properties_by_provider_and_htl_codes(provider.id, phtl_codes=phtl_ids)
        else:
            raise CatalogingServiceException(error_codes.INVALID_PROVIDER, context='Invalid provider %s' % provider)

    def get_all_expense_items(self, include_linked: bool):
        return self.__crs_client.get_all_expense_items(include_linked=include_linked)

    @atomic_operation
    def auto_create_sku_for_given_properties(self, ids):
        """
        Removed self.__property_repository.persist(treebo_property) as sqlalchemy handles flushing and committing to db
        And @atomic _operation handles rollbacks in case of exceptions
        :param ids:
        :return:
        """
        successful_property_ids = self.create_property_skus(ids)
        self.create_property_sku_categories(successful_property_ids)
        return successful_property_ids

    def create_property_skus(self, ids):
        from cataloging_service.domain import service_provider
        property_default_skus = self.__sku_repository.get_property_default_skus()
        sku_codes = {sku.code for sku in property_default_skus}
        treebo_properties = self.sget_all_live_properties(ids)
        if not treebo_properties:
            raise Exception("Properties {0} need to be live in order to create sku".format(ids))
        sku_categories = self.__sku_repository.rget_all_sku_categories_by_codes(
            list(DYNAMIC_PRICE_SKU_CATEGORY_TO_PREFIXES.keys())
        )
        dynamic_price_sku_categories = {
            sku_category.code: sku_category for sku_category in sku_categories
        }
        successful_property_ids = set()
        for treebo_property in treebo_properties:
            try:
                self._add_property_skus(
                    service_provider,
                    treebo_property,
                    dynamic_price_sku_categories,
                    property_default_skus,
                    sku_codes,
                )
                successful_property_ids.add(treebo_property.id)
            except Exception as ex:
                logger.exception(
                    "Exception while adding property_sku for {0} msg={1}".format(
                        treebo_property.id, str(ex)
                    )
                )
        if not successful_property_ids:
            raise Exception(
                "Sku could not be created for given properties {0}. Please check property status".format(ids)
            )
        self.publish_properties(
            [treebo_property.id for treebo_property in treebo_properties],
            PropertyMessageActions.ADMIN_CREATE.value,
        )
        return successful_property_ids

    def _add_property_skus(
        self,
        service_provider,
        treebo_property,
        dynamic_price_sku_category,
        property_default_skus,
        property_default_sku_codes,
    ):
        service_provider.sku_service.create_new_sku_for_property(
            treebo_property,
            dynamic_price_sku_category
        )
        applicable_skus = service_provider.sku_service.applicable_skus_to_property(
            treebo_property,
            sku_codes=property_default_skus,
        )
        created_property_skus = self.get_created_property_skus(
            treebo_property, applicable_skus, property_default_sku_codes
        )
        if created_property_skus:
            self.__property_repository.persist_all(created_property_skus)

    @staticmethod
    def _is_dynamic_pricing_sku(sku):
        sku_prefix = (sku.name.split(' ')[0]).upper()
        dynamic_sku_prefixes = set(itertools.chain(*DYNAMIC_PRICE_SKU_CATEGORY_TO_PREFIXES.values()))
        return sku_prefix in dynamic_sku_prefixes or EXTRA_ADULT in sku_prefix

    @atomic_operation
    def create_property_sku_categories(self, ids):
        treebo_properties = self.sget_all_live_properties(ids)
        for property_details in treebo_properties:
            property_id = property_details.id
            sku_categories = property_details.sku_categories
            available_property_sku_category_ids = [sku_category.id for sku_category in sku_categories]
            property_skus = property_details.sku_s
            property_sku_category_ids = list({sku.category_id for sku in property_skus})
            properties_sku_categories_id_to_be_created = [catg_id for catg_id in property_sku_category_ids if
                                                          catg_id not in available_property_sku_category_ids]
            if properties_sku_categories_id_to_be_created:
                try:
                    for category_id in properties_sku_categories_id_to_be_created:
                        insert_sku_categories = properties_sku_categories.insert().values(property_id=property_id,
                                                                                          sku_category_id=category_id)
                        self.__property_repository.session().execute(insert_sku_categories)
                    self.__property_repository.session().commit()
                except Exception as e:
                    logger.exception(
                        "Error while populating sku categories for property due to exception {0}".format(str(e)))
                    self.__property_repository.session().rollback()

    def get_created_property_skus(self, treebo_property, skus, property_default_skus):
        property_skus = []
        pre_existing_property_sku_ids = {property_sku.id for property_sku in treebo_property.sku_s}
        for sku in skus:
            if sku.id in pre_existing_property_sku_ids:
                continue
            is_dynamic_pricing_applicable = self._is_dynamic_pricing_sku(sku)
            is_saleable_and_active = sku.code in property_default_skus or is_dynamic_pricing_applicable
            property_skus.append(
                PropertySku(
                    display_name=sku.display_name,
                    property_id=treebo_property.id,
                    sku_id=sku.id,
                    saleable=True if is_saleable_and_active else False,
                    status=StandardStatusChoices.ACTIVE if is_saleable_and_active else StandardStatusChoices.INACTIVE,
                    sell_separate=True,
                    rack_rate=DEFAULT_RACK_RATE_FOR_DYNAMIC_PRICING_SKUS if is_dynamic_pricing_applicable else None,
                )
            )
        return property_skus

    def scheck_if_sku_exists_for_property_and_service(self, property_stock, param_service):
        return self.__property_repository.rcheck_if_stock_exists_by_property_and_service(property_stock.property_id,
                                                                                         property_stock.sku_id,
                                                                                         param_service.id)

    def sget_sku_for_given_property_and_sku_codes(self, given_property, sku_codes):
        """
        An exception block for handling untoward values supplied in the codes
        :param given_property:
        :param sku_codes:
        :return:
        """
        if given_property and isinstance(sku_codes, list):
            property_skus = self.__property_repository.rget_all_property_skus_by_property_and_sku(given_property.id,
                                                                                                  sku_codes)
            if not property_skus:
                raise CatalogingServiceException(error_codes.PROPERTY_SKU_NOT_FOUND, send_error_mail=False)
            return property_skus
        else:
            logger.error("Sku codes not supported")
            raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA,
                                             context="Empty list of Sku or invalid format")

    def create_property_sku_service_entry_for_activation(self, property_stocks, param_service):
        stocks_to_activate = []
        for property_stock in property_stocks:
            # check if entry exists for given property, sku and service
            if type(property_stock) is PropertySku and not self \
                    .scheck_if_sku_exists_for_property_and_service(property_stock, param_service):
                stock_activation = SkuActivation(property_id=property_stock.property_id,
                                                 sku_id=property_stock.sku_id,
                                                 service_id=param_service.id)
                stocks_to_activate.append(stock_activation)
        if stocks_to_activate:
            # Sqlalchemy autoflush not active for api calls so have to add it to session explicitly
            return self.__property_repository.persist_all(stocks_to_activate)
        return None

    def scheck_if_sku_exists_for_property_and_all_services(self, property_stock):
        """
        The count/length of params signifies the number of services the sku should be validated against for a
        given property, should be equal to the entries in SkuActivation table, as each entry  of property, sku and
        service is unique and is correspondent to a service entry in Params table, hence the length equality check
        The records in tables are unique hence no ambiguity in length check!!
        :param property_stock:
        :return: boolean value
        """
        from cataloging_service.domain import service_provider
        params = service_provider.meta_service.sget_entity_params('SkuActivation', 'service_id', True)
        count_of_property_sku_service = self.__property_repository. \
            rcount_of_stock_for_property_and_all_services(property_stock.property_id, property_stock.sku_id)
        return len(params) == count_of_property_sku_service

    def activate_property_stock(self, property_stocks):
        """
        If a property sku has been linked with all services defined in params and is inactive, then activate it
        :param property_stocks:
        :return:
        """
        to_be_activated_property_stocks = [property_stock for property_stock in property_stocks
                                           if self.scheck_if_sku_exists_for_property_and_all_services(property_stock)
                                           and property_stock.status == StandardStatusChoices.INACTIVE]
        if to_be_activated_property_stocks:
            for to_be_activated_property_stock in to_be_activated_property_stocks:
                to_be_activated_property_stock.status = StandardStatusChoices.ACTIVE
            return self.__property_repository.persist_all(to_be_activated_property_stocks)
        return None

    @atomic_operation
    def update_bundles_and_sku_activation_status_for_property(self, cs_id, service_name, sku_codes):
        from cataloging_service.domain import service_provider
        hotel_property = self.get_property(cs_id)
        service_param = service_provider.meta_service \
            .sget_entity_param_by_key_val('SkuActivation', 'service_id', service_name, True)
        property_skus = self.sget_sku_for_given_property_and_sku_codes(hotel_property, sku_codes=sku_codes)
        if property_skus:
            updated_stocks = self.create_property_sku_service_entry_for_activation(property_skus, service_param)
            self.activate_property_stock(property_skus)
            return updated_stocks
        return None

    def sget_property_sku_by_ids(self, ids=None):
        return self.__property_repository.rget_property_sku_by_ids(ids)

    def publish_property_skus(self, ids=None):
        property_skus = self.__property_repository.rget_property_sku_by_ids(ids)
        messages = [PropertySkuWrapper(property_sku, False, False).get_json() for property_sku in property_skus]
        for message in messages:
            self.__messaging_service.publish_property_sku_message(message)

    @atomic_operation
    def make_given_property_sku_unsaleable(self, property_ids):
        property_skus = self.sget_property_sku_by_ids(property_ids)
        for property_sku in property_skus:
            property_sku.saleable = False

    @atomic_operation
    def make_given_property_sku_saleable(self, property_ids):
        property_skus = self.sget_property_sku_by_ids(property_ids)
        for property_sku in property_skus:
            property_sku.saleable = True

    def get_property_skus(self, property_id, sku_codes, filter=None):
        property_skus = self.__property_repository.rget_all_skus_for_property(property_id, sku_codes, filter)
        if not property_skus:
            raise CatalogingServiceException(error_codes.PROPERTY_SKU_NOT_FOUND)
        skus = []
        for property_sku in property_skus:
            sku = property_sku.sku
            sku.saleable = property_sku.saleable
            sku.rack_rate = property_sku.rack_rate
            sku.sell_separate = property_sku.sell_separate
            sku.extra_information = property_sku.extra_information
            skus.append(sku)
        return skus

    def get_property_skus_v2(self, property_id, sku_codes=None, sku_category_codes=None, for_inclusions=None):
        property_skus = self.__sku_repository.get_property_skus(
            property_id, sku_codes=sku_codes, sku_category_codes=sku_category_codes, for_inclusions=for_inclusions)

        skus = []
        for property_sku in property_skus:
            sku = property_sku.sku
            skus.append(SkuDto(code=sku.code, sku_category_code=sku.category.code, name=sku.name,
                               display_name=sku.display_name, saleable=property_sku.saleable,
                               rack_rate=property_sku.rack_rate, offering=sku.offering, frequency=sku.frequency,
                               default_sale_price=sku.default_sale_price, default_list_price=sku.default_list_price,
                               is_property_inclusion=sku.is_property_inclusion, tax_at_room_rate=sku.tax_at_room_rate))
        return skus

    def search_properties(self, id, statuses, fuzzy_name, paginate=True, city_id=None):
        statuses = [s for s in statuses.split(',') if s]
        if statuses and not set(statuses).issubset(PropertyChoices.STATUS_CHOICES):
            raise ValidationError('Invalid status: {s}'.format(s=statuses),
                                  detail='Must be one of {s}'.format(s=PropertyChoices.STATUS_CHOICES))
        if fuzzy_name:
            properties = self.__property_repository.search_properties(
                id=id,
                city_id=city_id,
                statuses=statuses,
                order_by_created_at=False
            )
            properties = filter_fuzzy(fuzzy_name, query=properties)
        else:
            properties = self.__property_repository.search_properties(
                id=id,
                city_id=city_id,
                statuses=statuses,
                order_by_created_at=True
            )

        if paginate:
            properties = Pagination.paginate(properties, error_out=False)
        return properties

    def get_hotel_owner(self, property_id):
        owners = self.ownership_repository.get_ownership(property_id)
        owner_ids = [owner.owner_id for owner in owners if owner]
        if owner_ids:
            owner_details = self.owner_repository.get_owner_by_owner_id(owner_ids)
            return owner_details
        return []

    @atomic_operation
    def update_hotel_owner_details(self, property_id, parsed_request):
        property = self.__property_repository.get_property(property_id)
        if not property:
            raise CatalogingServiceException(context='Property not found', send_error_mail=False)

        owners = self.ownership_repository.get_ownership(property_id)
        owner_ids = [owner.owner_id for owner in owners if owner]

        if not owner_ids:
            new_owner_details = self.create_hotel_owner(parsed_request)
            self.create_hotel_ownership(new_owner_details, property_id)
        else:
            self.ownership_repository.delete_ownership(property_id)

            new_owner_details = self.create_hotel_owner(parsed_request)

            self.create_hotel_ownership(new_owner_details, property_id)

        return new_owner_details

    def create_hotel_ownership(self, new_owner_details, property_id):
        ownership_details = []
        for owner_detail in new_owner_details:
            ownership = Ownership()
            ownership.owner_id = owner_detail.id
            ownership.property_id = property_id
            ownership.primary = True

            ownership_details.append(ownership)
        self.ownership_repository.persist_all(ownership_details)

    def create_hotel_owner(self, parsed_request):
        owner_details = []
        for owner_detail in parsed_request['owner_details']:
            owner = Owner()
            owner.first_name = owner_detail['first_name']
            owner.last_name = owner_detail['second_name']
            owner.email = owner_detail['email']
            owner.phone_number = owner_detail['contact_number']
            owner_details.append(owner)
        new_owner_details = self.owner_repository.persist_all(owner_details)
        return new_owner_details

    def get_property_ids(self, statuses, modified_date):
        return self.__property_repository.get_property_ids(statuses, modified_date)

    @atomic_operation
    def rollover_current_business_date(self, property_id, business_date=None):
        property = self.__property_repository.get_property(property_id)

        if get_current_tenant_id() == TenantClient.get_default_tenant():
            if property.current_business_date == dateutils.current_date():
                raise CatalogingServiceException(error_codes.ROLLOVER_BEYOND_CURRENT_DATE,
                                                 "Can't rollover business date beyond current calendar date")

        self.__property_repository.rollover_current_business_date(property_id, business_date=business_date)
        sellers = self.seller_repository.rollover_current_business_date_for_all_sellers(property_id,
                                                                                        business_date=business_date)
        cache.expire_cache(property)
        [cache.expire_cache(seller) for seller in sellers]

        self.publish_properties([property_id], PropertyMessageActions.BUSINESS_DATE_ROLLOVER.value)
        self.publish_sellers(sellers)
        # Refresh Cache
        return property

    def publish_sellers(self, sellers):
        messages = [SellerWrapper(seller, False, False).get_json() for seller in sellers]
        for message in messages:
            self.__messaging_service.publish_seller_message(message)

    @staticmethod
    def is_property_having_youtube_video_url(property):
        for property_video in property.property_videos:
            if property_video.youtube_video_url:
                return True
        return False

    def get_test_properties(self):
        return self.__property_repository.get_test_property_ids()

    @staticmethod
    def get_brand_for_cost_center_id(brand_name):
        cost_center_brand_mapping = json.loads(TenantConfigService().get_tenant_configs_v2(config_name=COST_CENTER_CODE_MAPPING)[0].config_value)
        brand = cost_center_brand_mapping.get(brand_name)
        if not brand:
            return None
        brand_object = Brand.query.filter(Brand.code == brand.lower()).first()
        return brand_object
