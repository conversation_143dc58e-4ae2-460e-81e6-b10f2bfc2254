import itertools
import logging
from collections import defaultdict

import simplejson
from sqlalchemy.exc import IntegrityError

from cataloging_service.api.validators import BundleRuleValidator
from cataloging_service.client.dtos.role_privilege_dto import RolePrivilegesDTO
from cataloging_service.client.role_manager_client import RoleManagerClient
from cataloging_service.constants import error_codes
from cataloging_service.constants.constants import DYNAMIC_PRICE_SKU_CATEGORY_TO_PREFIXES
from cataloging_service.constants.model_choices import TaxTypeChoices, SkuTypeChoices
from cataloging_service.exceptions import CatalogingServiceException, ValidationException
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.messaging.messaging_wrappers import (
    Sku<PERSON><PERSON><PERSON><PERSON><PERSON>rapper,
    SkuWrapper,
    SkuUnderPropertyWrapper,
)
from cataloging_service.models import SellerSku, Sku, PropertySku
from cataloging_service.constants.model_choices import StandardStatusChoices
from cataloging_service.thread_locals import app_context
from cataloging_service.utils import Utils

logger = logging.getLogger(__name__)


class SkuService:
    def __init__(
        self, sku_repository, sku_redis_repository, messaging_service, property_repository
    ):
        self.__sku_repository = sku_repository
        self.__messaging_service = messaging_service
        self.__sku_redis_repository = sku_redis_repository
        self.__property_repository = property_repository

    def sget_all_sku_categories(self, ids=None):
        return self.__sku_repository.rget_all_sku_categories(ids)

    def sget_all_sku_categories_by_codes(self, codes=None):
        return self.__sku_repository.rget_all_sku_categories_by_codes(codes)

    def sget_all_sku_by_codes(self, codes=None):
        # Remember to delete the memoized cache in listener
        return self.__sku_repository.rget_sku_by_codes(codes)

    def sget_all_sku_by_given_codes_and_pagination(self, page, items_per_page, codes=None):
        return self.__sku_repository.rget_all_sku_by_status_and_codes(page, items_per_page, codes)

    def sget_all_stay_sku_by_codes(self, codes=None):
        return self.__sku_repository.rget_all_stay_sku_by_codes(codes)

    def sget_all_sku_charged_per_occupant(self, codes=None):
        return self.__sku_repository.rget_all_sku_charged_per_occupant(codes)

    def sget_all_sku_by_ids(self, ids=None):
        return self.__sku_repository.rget_sku_by_id(ids)

    def sget_skus_not_present_by_codes(self, codes=None):
        return self.__sku_repository.rget_skus_not_present_in_codes(codes)

    def sget_sku_category(self, id):
        sku_category = self.__sku_repository.rget_sku_category(id)

        if not sku_category:
            raise CatalogingServiceException(
                error_codes.SKU_CATEGORY_NOT_FOUND, context="sku_category id: %s" % id
            )

        return sku_category

    def sget_sku_category_by_code(self, code):
        sku_category = self.__sku_repository.rget_sku_category_by_code(code)

        if not sku_category:
            raise CatalogingServiceException(
                error_codes.SKU_CATEGORY_NOT_FOUND, context="sku_category code: %s" % code
            )

        return sku_category

    def sget_sku_by_code(self, code):
        sku = self.__sku_repository.rget_sku_by_code(code)

        if not sku:
            raise CatalogingServiceException(
                error_codes.SKU_NOT_FOUND, context="sku code: %s" % code
            )

        return sku

    def validate_bundle_rule(self, form_data):
        """
        Check the length of sku codes in the bundle rule and validate against the ones in db and raise
        :param form_data:
        :return: all skus
        """
        try:
            room_config_skus, occupancy_skus, chargeable_skus = None, None, None
            try:
                parsed_rule = BundleRuleValidator().loads(form_data)
            except Exception as e:
                raise CatalogingServiceException(
                    error_codes.INVALID_RULE_DEFINITION, context=str(e)
                )

            room_configs = parsed_rule.get("room_config_skus", None)
            occupancy = parsed_rule.get("occupancy_skus", None)
            chargeable_sku_per_occupant = parsed_rule.get("chargeable_skus_per_occupant", None)
            if (
                room_configs
                and occupancy
                and chargeable_sku_per_occupant
                and isinstance(room_configs, dict)
                and isinstance(occupancy, dict)
                and isinstance(chargeable_sku_per_occupant, dict)
            ):

                room_config_list, occupancy_list, chargeable_list = (
                    list(room_configs.values()),
                    list(occupancy.values()),
                    list(chargeable_sku_per_occupant.values()),
                )

                room_config_skus = self.sget_all_sku_by_codes(room_config_list)
                occupancy_skus = self.sget_all_sku_by_codes(occupancy_list)
                chargeable_skus = self.sget_all_sku_by_codes(chargeable_list)

                if (
                    (len(room_configs) != len(room_config_skus))
                    or (len(occupancy) != len(occupancy_skus))
                    or (len(chargeable_sku_per_occupant) != len(chargeable_skus))
                ):
                    raise CatalogingServiceException(
                        error_codes.INVALID_RULE_DEFINITION, send_error_mail=False
                    )

            return room_config_skus, occupancy_skus, chargeable_skus
        except Exception:
            raise

    def scheck_if_bundle_rule_is_unique(self):
        """
        Checks if a bundle rule is unique
        :return: boolean
        """
        from cataloging_service.domain import service_provider

        params = service_provider.meta_service.sget_entity_params("Sku", "bundle_rule", True)
        return False if len(params) > 1 else True

    def scheck_if_sku_exists(self, identifier):
        return self.__sku_repository.rcheck_if_sku_identifier_exists(identifier)

    def update_identifier_in_sku(self, sku):
        list_of_sku_count_tuples = [(sku_bundle.sku, sku_bundle.count) for sku_bundle in sku.bundle]
        identifier = self.unique_identifier_for_sku_and_count_tuple(list_of_sku_count_tuples)
        if self.scheck_if_sku_exists(identifier):
            raise Exception("Sku already defined with code %s" % identifier)
        return identifier

    def update_tag_for_parent_sku(self, sku):
        from cataloging_service.domain import service_provider

        room_config_sku_bundle, sku_bundle_adult, sku_bundle_child = None, None, None
        room_types = service_provider.room_service.get_all_room_types_by_codes(codes=None)
        room_type_set = {room_type.type for room_type in room_types}
        for sku_bundle in sku.bundle:
            if sku_bundle.sku.name in room_type_set:
                room_config_sku_bundle = sku_bundle
            elif "adult" in sku_bundle.sku.name.lower():
                sku_bundle_adult = sku_bundle
            elif "child" in sku_bundle.sku.name.lower():
                sku_bundle_child = sku_bundle
        adult_count = sku_bundle_adult.count if sku_bundle_adult else 0
        child_count = sku_bundle_child.count if sku_bundle_child else 0
        total_adult_count = adult_count + 1
        if child_count:
            return str.format(
                "%s-%s-%s" % (room_config_sku_bundle.sku.name, total_adult_count, child_count)
            )
        else:
            return str.format("%s-%s" % (room_config_sku_bundle.sku.name, total_adult_count))

    def create_unique_identifier_for_sku(self, form):
        identifier = str.format("%s:%s:%s" % ("sku", str.lower(form.name.data), 0))
        if self.scheck_if_sku_exists(identifier):
            raise Exception("Sku already defined with code %s" % identifier)
        return identifier

    def unique_identifier_for_sku_and_count_tuple(self, items):
        # Sort the sku names alphabetically
        items.sort(key=lambda sku_and_count_tuple: sku_and_count_tuple[0].id)
        created_str = "sku"
        for sku, count in items:
            created_str = str.format("%s:%s:%s" % (created_str, str.lower(sku.name), count))
        return created_str

    def create_skus(
        self, room_config_dict, dynamic_price_sku_category
    ):
        """
        https://stackoverflow.com/a/43235196/1790760
        rule {<sku>:<directly proportional to occupancy>}
        http://docs.sqlalchemy.org/en/latest/orm/basic_relationships.html#association-object
        https://gist.github.com/SuryaSankar/10091097
        The bundle creation can be optimized with a pre-lookup, check for max id created for bundle for sku_room_type
        and get the adult_count_index by subtracting from room_config.adults
        :param room_config_dict
        :param dynamic_price_sku_category
        :return:
        """
        # Default value not specified in get as its handled while creating dictionary
        tax_type = room_config_dict.get("tax_type")
        sku_type = room_config_dict.get("sku_type")
        sku_category = room_config_dict.get("sku_category")
        adults = room_config_dict.get("adults")
        children = room_config_dict.get("children")
        sku_room_type = room_config_dict.get("sku_room_type")
        sku_adult = room_config_dict.get("sku_adult")
        sku_child = room_config_dict.get("sku_child")
        occupant_equivalent_sku_list = room_config_dict.get("chargeable_skus")
        bundle_rule = room_config_dict.get("bundle_rule")
        created_skus = []

        if adults and adults > 0:
            for adult_count_index in range(adults):
                # chargeable sku as per bundle rule is added for every bundle
                # and is same as current occupancy count adult_count_index
                list_of_sku_tuples_adult = [
                    (
                        sku,
                        sku.flat_count_for_creation
                        if sku.flat_count_for_creation
                        else adult_count_index + 1,
                    )
                    for sku in occupant_equivalent_sku_list
                ]
                list_of_sku_tuples_adult.extend(
                    [
                        (sku_room_type, sku_room_type.flat_count_for_creation),
                        (sku_adult, adult_count_index),
                    ]
                )

                tag = str.format("%s-%s" % (sku_room_type.name, adult_count_index + 1))
                bundle_name = tag
                bundle_identifier = self.unique_identifier_for_sku_and_count_tuple(
                    list_of_sku_tuples_adult
                )
                if not self.scheck_if_sku_exists(bundle_identifier):
                    created_sku_adult = Sku(
                        name=bundle_name,
                        saleable=True,
                        is_modular=False,
                        tax_type=tax_type,
                        sku_type=sku_type,
                        category=sku_category,
                        identifier=bundle_identifier,
                        sku_count=len(list_of_sku_tuples_adult),
                        bundle_rule=bundle_rule,
                        tag=tag,
                    )
                    created_sku_adult.add_skus(list_of_sku_tuples_adult)
                    created_skus.append(created_sku_adult)
                for category_code, dynamic_price_sku_prefixes in DYNAMIC_PRICE_SKU_CATEGORY_TO_PREFIXES.items():
                    for sku_prefix in dynamic_price_sku_prefixes:
                        dynamic_pricing_sku_tag = "{0} {1}-0".format(sku_prefix, tag)
                        identifier = "{0}:{1}".format(sku_prefix.lower(), tag.lower())
                        if not self.scheck_if_sku_exists(identifier):
                            created_dynamic_pricing_sku = Sku(
                                name=dynamic_pricing_sku_tag,
                                display_name=dynamic_pricing_sku_tag.upper(),
                                saleable=True,
                                is_modular=True,
                                tax_type=tax_type,
                                sku_type=SkuTypeChoices.sku,
                                category=dynamic_price_sku_category[category_code],
                                identifier=identifier,
                                sku_count=0,
                                tag=dynamic_pricing_sku_tag,
                                is_property_inclusion=True,
                            )
                            created_skus.append(created_dynamic_pricing_sku)
                if children and children > 0:
                    for child_count_index in range(children):
                        # count of below sku has to be proportional to current occupancy count
                        list_of_sku_tuples_children = [
                            (
                                sku,
                                sku.flat_count_for_creation
                                if sku.flat_count_for_creation
                                else (adult_count_index + 1) + (child_count_index + 1),
                            )
                            for sku in occupant_equivalent_sku_list
                        ]
                        list_of_sku_tuples_children.extend(
                            [
                                (sku_room_type, sku_room_type.flat_count_for_creation),
                                (sku_adult, adult_count_index),
                                (sku_child, child_count_index + 1),
                            ]
                        )
                        tag = str.format(
                            "%s-%s-%s"
                            % (sku_room_type.name, adult_count_index + 1, child_count_index + 1)
                        )
                        bundle_name = tag
                        bundle_identifier = self.unique_identifier_for_sku_and_count_tuple(
                            list_of_sku_tuples_children
                        )
                        if not self.scheck_if_sku_exists(bundle_identifier):
                            created_sku_child = Sku(
                                name=bundle_name,
                                saleable=True,
                                is_modular=False,
                                tax_type=tax_type,
                                sku_type=sku_type,
                                category=sku_category,
                                identifier=bundle_identifier,
                                sku_count=len(list_of_sku_tuples_children),
                                bundle_rule=bundle_rule,
                                tag=tag,
                            )
                            created_sku_child.add_skus(list_of_sku_tuples_children)
                            created_skus.append(created_sku_child)

        return created_skus

    def _get_dict_parameter_for_create_skus_method(self, room_configs):
        from cataloging_service.domain import service_provider

        list_of_room_config_dicts = []

        # Get the standard sku's to be used while creating bundles, to avoid fetching them repeatedly
        tax_type = TaxTypeChoices.unit
        sku_type = SkuTypeChoices.bundle
        sku_category = self.sget_sku_category_by_code("stay")

        # Fetch and validate sku_codes_defined_in_bundle_rule
        bundle_rule_json = service_provider.meta_service.sget_bundle_rule()
        room_config_skus, occupancy_skus, chargeable_skus = self.validate_bundle_rule(
            bundle_rule_json.value
        )

        for room_config in room_configs:
            room_type = str(room_config.room_type.type).lower()
            room_type_adult = str.format("%s-%s" % (room_type, "adult")).lower()
            room_type_child = str.format("%s-%s" % (room_type, "child")).lower()
            room_type_extra_adult = "{0}-{1}".format(room_type, "extra-adult").lower()
            room_type_subsequent_extra_adult = "{0}-{1}".format(room_type, "subsequent-extra-adult").lower()
            # Fetch the first match
            sku_adult = next(
                (sku for sku in occupancy_skus if str.lower(sku.name) == room_type_adult), None
            )
            sku_extra_adult = next(
                (sku for sku in occupancy_skus if sku.name.lower() == room_type_extra_adult), None
            )
            sku_subsequent_extra_adult = next(
                (sku for sku in occupancy_skus if sku.name.lower() == room_type_subsequent_extra_adult), None
            )
            sku_child = next(
                (sku for sku in occupancy_skus if str.lower(sku.name) == room_type_child), None
            )
            sku_room_type = next(
                (sku for sku in room_config_skus if str.lower(sku.name) == room_type), None
            )
            if not sku_room_type:
                continue

            room_config_dict_param = dict(
                tax_type=tax_type,
                sku_type=sku_type,
                sku_category=sku_category,
                sku_adult=sku_adult,
                sku_extra_adult=sku_extra_adult,
                sku_subsequent_extra_adult=sku_subsequent_extra_adult,
                sku_child=sku_child,
                chargeable_skus=chargeable_skus,
                sku_room_type=sku_room_type,
                adults=room_config.adults,
                children=room_config.children,
                bundle_rule=bundle_rule_json,
            )
            list_of_room_config_dicts.append(room_config_dict_param)

        return list_of_room_config_dicts

    @atomic_operation
    def create_new_sku_for_property(
        self, treebo_property, dynamic_price_sku_category
    ):
        """
        :param treebo_property:
        :param dynamic_price_sku_category:
        :return:

        Explanation:
        # list_of_bundle_list_for_each_room_config = list(map(self.create_bundles, room_configs))
        # joined_list_of_bundles = list(itertools.chain(*list_of_bundle_list_for_each_room_config))
        # if joined_list_of_bundles:
        #     self.__sku_repository.persist_all(joined_list_of_bundles)
        Above is removed as auto_commit is enabled and sqlalchemy intelligently flushes all changes and commits to DB
        @atomic operation takes care of rollback in case of any error. Better to annotate with decorator rather than
        manually handling rollbacks here in exception block
        """
        try:
            # Create a dictionary and send for bundle creation
            room_config_dicts = self._get_dict_parameter_for_create_skus_method(
                treebo_property.room_type_configurations
            )
            with self.__sku_repository.session().no_autoflush:
                # LC is the faster, commented out # list(map(self.create_skus, room_config_dicts))
                import itertools

                created_skus = list(
                    itertools.chain(
                        *[
                            self.create_skus(
                                room_config_dict,
                                dynamic_price_sku_category,
                            )
                            for room_config_dict in room_config_dicts
                        ]
                    )
                )
                created_skus = self.__sku_repository.persist_all(created_skus)
                if created_skus:
                    self.publish_sku([sku.id for sku in created_skus])
        except Exception as e:
            logger.error(e)
            raise

    def _get_applicable_skus_for_property(self, room_config_dict):
        applicable_skus = [
            room_config_dict.get("sku_room_type"),
            room_config_dict.get("sku_adult"),
            room_config_dict.get("sku_child"),
            room_config_dict.get("sku_extra_adult"),
            room_config_dict.get("sku_subsequent_extra_adult"),
        ]
        applicable_skus.extend(room_config_dict.get("chargeable_skus"))
        return applicable_skus

    def _get_tags(self, room_config_dict):
        tags = []
        sku_room_type = room_config_dict.get("sku_room_type")
        adults = room_config_dict.get("adults")
        children = room_config_dict.get("children")

        if adults and adults > 0:
            for adult_count_index in range(adults):
                tag = str.format("%s-%s" % (sku_room_type.name, adult_count_index + 1)).lower()
                tags.append(tag)
                dynamic_sku_prefixes = list(itertools.chain(*DYNAMIC_PRICE_SKU_CATEGORY_TO_PREFIXES.values()))
                dynamic_pricing_sku_tags = [
                    "{0} {1}-0".format(sku_prefix.lower(), tag)
                    for sku_prefix in dynamic_sku_prefixes
                ]
                tags.extend(dynamic_pricing_sku_tags)
                if children and children > 0:
                    for child_count_index in range(children):
                        tag = str.format(
                            "%s-%s-%s"
                            % (sku_room_type.name, adult_count_index + 1, child_count_index + 1)
                        ).lower()
                        tags.append(tag)
        return tags

    def applicable_skus_to_property(
        self,
        treebo_property,
        sku_codes=None,
    ):
        try:
            room_config_dicts = self._get_dict_parameter_for_create_skus_method(
                treebo_property.room_type_configurations
            )
            import itertools

            applicable_skus = set(
                itertools.chain(
                    *[
                        self._get_applicable_skus_for_property(room_config_dict)
                        for room_config_dict in room_config_dicts
                    ]
                )
            )

            tags = list(
                itertools.chain(
                    *[
                        self._get_tags(room_config_dict)
                        for room_config_dict in room_config_dicts
                    ]
                )
            )
            matching_skus = self.__sku_repository.rget_skus_by_tags(tags)
            applicable_skus.update(matching_skus)
            if sku_codes:
                applicable_skus.update(sku_codes)
            return applicable_skus
        except Exception as e:
            raise Exception("Error getting applicable skus for property", e)

    def sget_newly_created_sku(self):
        return self.__sku_repository.rget_newly_created_sku()

    def sget_relevant_skus_for_given_room_config(self, room_config_string):
        from cataloging_service.domain import service_provider

        result = []
        validated, pattern = Utils.is_valid_translator_param(room_config_string.strip())
        if not validated:
            raise CatalogingServiceException(
                error_codes.INVALID_REQUEST_DATA, context="Query not well formed"
            )
        room_configs = pattern.findall(room_config_string)

        # Fetch and validate sku_codes_defined_in_bundle_rule
        bundle_rule_json = service_provider.meta_service.sget_bundle_rule()
        room_config_skus, occupancy_skus, chargeable_skus = self.validate_bundle_rule(
            bundle_rule_json.value
        )

        for room_config in room_configs:
            # OAK, 2, 1
            configs = room_config.strip().split("-")
            room_type = configs[0].strip().lower()
            adults = int(configs[1]) - 1
            children = int(configs[2])
            total_count_of_occupancy = int(configs[1]) + int(configs[2])
            sku_room_type = next(
                (sku for sku in room_config_skus if str.lower(sku.name) == room_type), None
            )
            # Fetch the first match
            sku_adult = next(
                (sku for sku in occupancy_skus if str.lower(sku.name) == "adult"), None
            )
            sku_child = next(
                (sku for sku in occupancy_skus if str.lower(sku.name) == "child"), None
            )

            # Create a list of tuples to calculate identifier
            list_of_sku_tuples = [(sku, total_count_of_occupancy) for sku in chargeable_skus]
            list_of_sku_tuples.extend(
                [(sku_room_type, 1), (sku_adult, adults), (sku_child, children)]
            )
            identifier = self.unique_identifier_for_sku_and_count_tuple(list_of_sku_tuples)

            relevant_sku = self.__sku_repository.rget_sku_by_identifier(identifier)
            if relevant_sku:
                result.append(relevant_sku)
        return result

    def sget_relevant_skus_for_given_room_config_and_hotels(
        self, list_of_hotel_wise_sku_details, room_type_codes, configs
    ):
        room_type_codes = list(map(lambda x: x.lower(), room_type_codes)) if room_type_codes else []
        configs = list(map(lambda x: x.lower(), configs) if configs else [])

        def filter_room_types_and_config(sku_detail):
            room_type_present = (
                True  # assume true if room_type_code is empty to simulate all room types
            )
            if room_type_codes and sku_detail["room_code"].lower() not in room_type_codes:
                room_type_present = False
            room_config_present = sku_detail["room_config"].lower() in configs
            return room_type_present and room_config_present

        for property_detail in list_of_hotel_wise_sku_details:
            property_detail["property_sku_room_mapping"] = [
                sku_detail
                for sku_detail in property_detail["property_sku_room_mapping"]
                if filter_room_types_and_config(sku_detail)
            ]

        return list_of_hotel_wise_sku_details

    def sget_all_skus_for_hotels(self, hotel_ids):
        property_skus = self.__property_repository.get_property_skus(set(hotel_ids))
        skus = [x.sku for x in property_skus]
        return skus

    def sget_all_skus_for_given_hotels(self, hotel_ids):
        from cataloging_service.domain import service_provider

        room_type_to_room_code = {
            room_type.type: room_type.code
            for room_type in service_provider.room_service.get_all_room_types_by_codes()
        }

        property_skus = self.__property_repository.get_properties_skus(set(hotel_ids))

        property_wise_skus = defaultdict(list)
        for property_sku in property_skus:
            if not property_sku.sku.tag:
                continue
            room_type_config = self._format_config(property_sku.sku.tag)
            room_type, room_config = room_type_config.split("-")[0], "-".join(
                room_type_config.split("-")[1:]
            )
            if room_type not in room_type_to_room_code:
                continue
            val_dict = dict(
                property_sku=property_sku,
                room_type=room_type,
                room_config=room_config,
                room_code=room_type_to_room_code[room_type],
            )
            property_wise_skus[property_sku.property_id].append(val_dict)

        return property_wise_skus

    @staticmethod
    def _format_config(config):
        """
        expects config as OAK-1-1, OAK-1, OAK-1-0
        :param config:
        :return:
        """
        import re

        if re.match(r"^[a-zA-Z]+-[1-9]-0$", config):
            return config[0:-2]
        elif re.match(r"^[a-zA-Z]+-[1-9]$", config):
            return "%s-%s" % (config, "0")
        else:
            return config

    @staticmethod
    def build_schema_compatible_response(result, room_type_to_room_code):
        """
        format: [{"property_id": "123", "property_skus": [{"room_type_code": 'rt01', "room_config": '1-0', "sku": {}}]}]
        """
        response = []
        for key, value in result.items():
            val_list = []
            for val in value:
                room_type_config = val.pop("room_type_config")
                room_type, room_config = room_type_config.split("-")[0], "-".join(
                    room_type_config.split("-")[1:]
                )
                room_code = room_type_to_room_code[room_type]
                val_list.append(
                    {
                        "room_type": room_type,
                        "room_config": room_config,
                        "property_sku": val["property_sku"],
                        "room_code": room_code,
                    }
                )
            response_dict = {"property_id": key, "property_sku_room_mapping": val_list}
            response.append(response_dict)
        return response

    def sget_associated_sku_for_room_type(self, sku_name):
        sku = self.__sku_repository.rget_sku_by_name_category_sku_type_and_is_modular(
            sku_name.lower(), "stay", True
        )
        return sku.code if sku else None

    def get_room_config_from_sku_redis(self, sku_codes):
        response = []
        sku_dict_list = self.__sku_redis_repository.get_given_sku(sku_codes=sku_codes)

        if None in sku_dict_list:
            # if missed first time
            self.bulk_sync_to_redis(ids=None)
            sku_dict_list = self.__sku_redis_repository.get_given_sku(sku_codes=sku_codes)

        for sku_dict_value in sku_dict_list:
            if sku_dict_value:
                sku_dict = simplejson.loads(sku_dict_value)
                total_adult_count = (
                    sku_dict.get("rt_adult_sku_count") if sku_dict.get("rt_adult_sku_count") else 0
                )
                child_count = (
                    sku_dict.get("rt_child_sku_count") if sku_dict.get("rt_child_sku_count") else 0
                )
                response.append(
                    dict(
                        sku_code=sku_dict.get("code", None),
                        room_type=dict(
                            code=sku_dict.get("rt_code", None), type=sku_dict.get("rt", None)
                        ),
                        room_config="%s-%s" % (total_adult_count, child_count),
                        adults=total_adult_count,
                        children=child_count,
                    )
                )

        return response

    def validate_sku_object(self, sku):
        if sku.is_modular or (not sku.is_modular and sku.sku_count == len(sku.bundle)):
            return True
        else:
            return False

    def bulk_sync_to_redis(self, ids):
        all_skus = self.sget_all_sku_by_ids(ids)
        room_type_dict = self.get_room_type_dict()
        self.__sku_redis_repository.save_all_sku(all_sku=all_skus, room_type_dict=room_type_dict)

    def sync_sku_to_redis(self, sku):
        room_type_dict = self.get_room_type_dict()
        self.__sku_redis_repository.save_sku(sku=sku, room_type_dict=room_type_dict)

    def get_room_type_dict(self):
        from cataloging_service.domain import service_provider

        room_types = service_provider.room_service.get_all_room_types_by_codes()
        room_type_dict = {room_type.type: room_type for room_type in room_types}
        return room_type_dict

    def publish_categories(self, ids):
        categories = self.sget_all_sku_categories(ids)
        messages = [
            SkuCategoryWrapper(category, False, False).get_json() for category in categories
        ]
        for message in messages:
            self.__messaging_service.publish_sku_category_message(message)

    def publish_sku(self, ids):
        sku_s = self.sget_all_sku_by_ids(ids)
        messages = [
            SkuWrapper(sku, False, False).get_json()
            for sku in sku_s
            if self.validate_sku_object(sku)
        ]
        for message in messages:
            self.__messaging_service.publish_sku_message(message)

    def create_sku_for_combo(self, sku_category_code, combo):
        sku_category = self.sget_sku_category_by_code(code=sku_category_code)
        sku = self.__sku_repository.persist(
            Sku(
                category_id=sku_category.id,
                hsn_sac=sku_category.hsn_sac,
                is_active=True,
                name=combo.name,
                is_property_inclusion=False,
            )
        )
        sku_dict = {"sku_category_code": sku_category_code, "name": combo.name, "display_name": combo.display_name}
        self._create_new_seller_sku(sku_dict, sku, combo.seller_id)

        self.publish_sku([sku.id])
        return sku

    def create_sku_for_item_variants(self, item_variants, item_name, seller_id):
        sku_categories = [
            self.sget_sku_category_by_code(code=item_variant.sku_category_code)
            for item_variant in item_variants
        ]  # iterating calls to get one result for one item_variant

        skus = self.__sku_repository.persist_all(
            [
                Sku(
                    category_id=sku_category.id,
                    hsn_sac=sku_category.hsn_sac,
                    is_active=True,
                    name=item_name + ': ' + item_variant.name,
                    is_property_inclusion=False,
                )
                for item_variant, sku_category in zip(item_variants, sku_categories)
            ]
        )

        for item_variant, sku in zip(item_variants, skus):
            sku_dict = {"sku_category_code": item_variant.sku_category_code,
                        "name": item_name + ': ' + item_variant.name,
                        "display_name": item_variant.name}
            self._create_new_seller_sku(sku_dict, sku, seller_id)

        self.publish_sku([sku.id for sku in skus])
        return skus

    def create_sku_for_item(self, sku_category_code, item):
        sku_category = self.sget_sku_category_by_code(code=sku_category_code)
        sku = self.__sku_repository.persist(
            Sku(
                category_id=sku_category.id,
                hsn_sac=sku_category.hsn_sac,
                is_active=True,
                name=item.name,
                is_property_inclusion=False,
            )
        )
        sku_dict = {"sku_category_code": sku_category_code, "name": item.name, "display_name": item.display_name}
        self._create_new_seller_sku(sku_dict, sku, item.seller_id)
        self.publish_sku([sku.id])
        return sku

    def update_sku_for_item(self, sku_category_code, item):
        sku_category = self.sget_sku_category_by_code(code=sku_category_code)
        sku = self.__sku_repository.get_for_update(model=Sku, id=item.sku_id).first()
        sku = self.__sku_repository._update(
            Sku(
                id=sku.id,
                hsn_sac=sku_category.hsn_sac,
                category_id=sku_category.id,
                is_active=True,
                name=item.name,
                is_property_inclusion=False,
            )
        )
        self.publish_sku([sku.id])
        return sku

    def update_sku_for_combo(self, sku_category_code, combo):
        sku_category = self.sget_sku_category_by_code(code=sku_category_code)
        sku = self.__sku_repository.get_for_update(model=Sku, id=combo.sku_id).first()
        sku = self.__sku_repository._update(
            Sku(
                id=combo.sku_id,
                category_id=sku_category.id,
                hsn_sac=sku_category.hsn_sac,
                is_active=True,
                name=combo.name,
                is_property_inclusion=False,
            )
        )
        self.publish_sku([sku.id])
        return sku

    def update_sku_for_item_variants(self, item_variants):
        sku_categories = [
            self.sget_sku_category_by_code(code=item_variant.sku_category_code)
            for item_variant in item_variants
        ]  # iterating calls to get one result for one item_variant

        skus = self.__sku_repository.get_for_update(
            model=Sku, id=[item_variant.sku_id for item_variant in item_variants]
        ).all()
        skus = self.__sku_repository._update_all(
            [
                Sku(
                    id=item_variant.sku_id,
                    category_id=sku_category.id,
                    hsn_sac=sku_category.hsn_sac,
                    is_active=True,
                    name=item_variant.name,
                    is_property_inclusion=False,
                )
                for sku_category, item_variant in zip(sku_categories, item_variants)
            ]
        )

        self.publish_sku([sku.id for sku in skus])
        return skus

    def disable_skus(self, sku_ids_list):
        skus_to_disable = self.__sku_repository.get_for_update(model=Sku, id=sku_ids_list).all()

        for sku in skus_to_disable:
            sku.is_active = False

        return self.__sku_repository._update_all(skus_to_disable)

    def publish_sku_under_property(self, ids):
        property_skus = self.__sku_repository.get_property_sku_by_ids(ids)
        messages = [
            SkuUnderPropertyWrapper(property_sku, False, False).get_json()
            for property_sku in property_skus
        ]
        for message in messages:
            self.__messaging_service.publish_sku_under_property_message(message)

    @atomic_operation
    def create_sku_under_a_property(self, property_sku_dict, sku_dict, user_type):
        role_privilege_dtos = RoleManagerClient.get_privilege_by_role_name(user_type)
        role_privilege = RolePrivilegesDTO.array_to_dict(role_privilege_dtos)
        role_privilege_code = [rp.privilege_code for rp in role_privilege_dtos]
        logger.info("Role privilege for role {user_type} : {role_privilege_code}".format(
            user_type=user_type, role_privilege_code=role_privilege_code))
        if "CREATE_SKU_UNDER_A_PROPERTY" not in role_privilege:
            error_message = '{user_type} user not allowed to add sku.'.format(user_type=user_type)
            raise ValidationException(error_codes.INVALID_REQUEST_DATA, error_message)

        sku = self._create_new_sku(
            name=sku_dict["name"],
            category_code=sku_dict["category_code"],
            hsn_sac=sku_dict["hsn_sac"],
            offering=sku_dict["offering"],
            frequency=sku_dict["frequency"],
            property_id=property_sku_dict["property_id"],
            is_property_inclusion=sku_dict.get("is_property_inclusion", False),
            is_expense_item_sku=sku_dict.get("is_expense_item_sku", True)
        )

        property_sku = self._create_new_property_sku(property_sku_dict, sku)
        sku.rack_rate = property_sku.rack_rate

        return sku, property_sku

    @atomic_operation
    def update_sku_under_a_property(self, sku_code, property_sku_dict, sku_dict, user_type):
        role_privilege_dtos = RoleManagerClient.get_privilege_by_role_name(user_type)
        role_privilege = RolePrivilegesDTO.array_to_dict(role_privilege_dtos)
        role_privilege_code = [rp.privilege_code for rp in role_privilege_dtos]
        logger.info("Role privilege for role {user_type} : {role_privilege_code}".format(
            user_type=user_type, role_privilege_code=role_privilege_code))
        if "UPDATE_SKU_UNDER_A_PROPERTY" not in role_privilege:
            error_message = '{user_type} user not allowed to update sku.'.format(user_type=user_type)
            raise ValidationException(error_codes.INVALID_REQUEST_DATA, error_message)

        sku = self.sget_sku_by_code(sku_code)
        if sku_dict.get('frequency') or sku_dict.get('offering'):
            if sku.frequency != sku_dict['frequency']:
                sku.frequency = sku_dict['frequency']
            if sku.offering != sku_dict['offering']:
                sku.offering = sku_dict['offering']
            self.__sku_repository.save_sku(sku)

        property_sku = self.__sku_repository.get_property_sku(
            property_sku_dict["property_id"], sku.id
        )
        if property_sku_dict.get('rack_rate'):
            property_sku.rack_rate = property_sku_dict["rack_rate"]
        if property_sku_dict.get("description"):
            property_sku.description = property_sku_dict["description"]
        if property_sku_dict.get("extra_information"):
            property_sku.extra_information.update(property_sku_dict["extra_information"])
        if property_sku_dict.get("sell_separate") is not None:
            property_sku.sell_separate = property_sku_dict["sell_separate"]
        self.__sku_repository.save_property_sku(property_sku)
        return sku, property_sku

    @atomic_operation
    def _create_new_sku(
        self,
        name,
        category_code,
        hsn_sac,
        offering,
        frequency,
        property_id=None,
        sku_code=None,
        is_expense_item_sku=False,
        tax_at_room_rate=False,
        is_property_inclusion=False,
    ):
        sku_category = self.sget_sku_category_by_code(category_code)
        if hsn_sac != sku_category.hsn_sac:
            raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context='Invalid hsn sac')

        sku = Sku(
            name=name,
            category_id=sku_category.id,
            hsn_sac=hsn_sac,
            offering=offering,
            frequency=frequency,
            tax_at_room_rate=tax_at_room_rate,
            is_property_inclusion=is_property_inclusion,
        )
        if is_expense_item_sku:
            identifier = str.format('%s%s:%s' % ('expense_item:', 'sku', str.lower(name)))
            if sku_code:
                sku.code = sku_code
            app_context.is_expense_item_sku = True
        else:
            identifier = str.format('%s:%s:%s' % ('sku', str.lower(name), property_id))
        sku.identifier = identifier
        existing_sku = self.__sku_repository.rget_sku_by_identifier(sku.identifier)
        if existing_sku:
            return existing_sku
        try:
            self.__sku_repository.save_sku(sku)
        except IntegrityError as exception:
            raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context=exception.args[0])
        return sku

    @atomic_operation
    def _create_new_property_sku(self, property_sku_dict, sku):
        property_sku = PropertySku(
            property_id=property_sku_dict["property_id"],
            sku_id=sku.id,
            rack_rate=property_sku_dict["rack_rate"],
            status=StandardStatusChoices.ACTIVE,
            saleable=True,
            description=property_sku_dict["description"],
            extra_information=property_sku_dict["extra_information"],
            sell_separate=property_sku_dict["sell_separate"],
        )
        self.__sku_repository.save_property_sku(property_sku)
        return property_sku

    def _create_new_seller_sku(self, sku_dict, sku, seller_id):
        seller_sku = SellerSku(
            sku_id=sku.id,
            seller_id=seller_id,
            is_sellable=True,
            sku_category_code=sku_dict["sku_category_code"],
            name=sku_dict["name"],
            display_name=sku_dict["display_name"] if sku_dict["display_name"] else sku_dict["name"],
        )
        self.__sku_repository.save_seller_sku(seller_sku)
        return seller_sku

    def get_sku_categories_for_seller(self, seller_id):
        return self.__sku_repository.get_sku_categories_for_seller(seller_id)
