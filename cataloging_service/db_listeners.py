import logging

from sqlalchemy import event
from sqlalchemy.orm import Session

from cataloging_service.client import client_provider
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.messaging import message_objects, queue_messages
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.models import Property, Description, GuestFacingProcess, Landmark, Location, \
    NeighbouringPlace, \
    Ownership, PropertyDetail, Room, RoomTypeConfiguration, BankDetail, Owner, TransportStation, \
    TransportStationProperty, Restaurant, Bar, BanquetHall, PropertyAmenity, RoomAmenity, AmenityPublicWashroom, \
    AmenityElevator, AmenityParking, AmenityDisableFriendly, AmenitySwimmingPool, AmenityGym, AmenityPrivateCab, \
    AmenitySpa, AmenityLaundry, AmenityBreakfast, AmenityPayment, AmenityIntercom, AmenityHotWater, AmenityTV, \
    AmenityAC, AmenityHeater, AmenityStove, AmenityTwinBed, City, MicroMarket, Locality, PropertyImage, State, \
    SkuCategory, CityAlias, AmenitySummary, PropertyLandmark, Channel, SubChannel, Application, RoomType, Provider, \
    Sku, PropertySku, PricingPolicy, ProviderBrandMapping, PropertyBrandMapping, PricingMapping, Seller, SellerSku, \
    RuptubLegalEntityDetails, RoomRackRateModel, PropertyVideo
from cataloging_service.thread_locals import app_context
from cataloging_service.utils import Utils
from treebo_commons.request_tracing.context import request_context


logger = logging.getLogger(__name__)

property_repository = repo_provider.property_repository
seller_sku_repository = repo_provider.seller_sku_repository
sku_repository = repo_provider.sku_repository
channel_repository = repo_provider.channel_repository
messaging_service = service_provider.messaging_service
email_client = client_provider.email_client


# Never perform any Create, Update, Delete operations over here since it may lead to a cycle

def init_listeners():
    # Dummy method to be called in app initializer
    pass


@event.listens_for(Property, 'after_insert')
def on_property_insert(mapper, connection, target):
    logger.debug('Property insert event emitted')
    message_objects.register_new_property(target)


@event.listens_for(Property, 'after_update')
def on_property_update(mapper, connection, target):
    logger.debug('Property update event emitted')
    message_objects.register_updated_property(target)


@event.listens_for(Description, 'after_update')
@event.listens_for(Description, 'after_insert')
@event.listens_for(Description, 'after_delete')
def on_description_change(mapper, connection, target):
    logger.debug('Description change event emitted')
    property = _get_property_object(target)
    message_objects.register_updated_property(property)


@event.listens_for(GuestFacingProcess, 'after_update')
@event.listens_for(GuestFacingProcess, 'after_insert')
@event.listens_for(GuestFacingProcess, 'after_delete')
def on_guest_facing_details_change(mapper, connection, target):
    logger.debug('Guest facing details change event emitted')
    property = _get_property_object(target)
    message_objects.register_updated_property(property)


@event.listens_for(Landmark, 'after_update')
@event.listens_for(Landmark, 'after_insert')
@event.listens_for(Landmark, 'after_delete')
def on_landmark_details_change(mapper, connection, target):
    logger.debug('Landmark change event emitted')
    property_landmarks = target.property_landmarks
    for property_landmark in property_landmarks:
        property = _get_property_object(property_landmark)
        message_objects.register_updated_property(property)


@event.listens_for(SellerSku, 'after_insert')
def on_seller_sku_insert(mapper, connection, target):
    logger.debug('Seller sku change event emiitted')
    message_objects.register_new_seller_sku(target)


@event.listens_for(PropertyLandmark, 'after_update')
@event.listens_for(PropertyLandmark, 'after_insert')
@event.listens_for(PropertyLandmark, 'after_delete')
def on_property_landmark_details_change(mapper, connection, target):
    logger.debug('Property Landmark change event emitted')
    property = _get_property_object(target)
    message_objects.register_updated_property(property)


@event.listens_for(Location, 'after_update')
@event.listens_for(Location, 'after_insert')
@event.listens_for(Location, 'after_delete')
def on_location_change(mapper, connection, target):
    logger.debug('Location change event emitted')
    property = _get_property_object(target)
    message_objects.register_updated_property(property)


@event.listens_for(NeighbouringPlace, 'after_update')
@event.listens_for(NeighbouringPlace, 'after_insert')
@event.listens_for(NeighbouringPlace, 'after_delete')
def on_neighbourhood_change(mapper, connection, target):
    logger.debug('Neighbouring place change event emitted')
    property = _get_property_object(target)
    message_objects.register_updated_property(property)


@event.listens_for(Ownership, 'after_update')
@event.listens_for(Ownership, 'after_insert')
@event.listens_for(Ownership, 'after_delete')
def on_ownership_change(mapper, connection, target):
    logger.debug('Ownership change event emitted')
    property = _get_property_object(target)
    message_objects.register_updated_property(property)


@event.listens_for(BankDetail, 'after_update')
@event.listens_for(BankDetail, 'after_insert')
@event.listens_for(BankDetail, 'after_delete')
def on_ownership_change(mapper, connection, target):
    logger.debug('Bank Detail change event emitted')
    if target.property_detail:
        message_objects.register_updated_property(target.property_detail.property)


@event.listens_for(PropertyDetail, 'after_update')
@event.listens_for(PropertyDetail, 'after_insert')
@event.listens_for(PropertyDetail, 'after_delete')
def on_property_detail_change(mapper, connection, target):
    logger.debug('Property Detail change event emitted')
    property = _get_property_object(target)
    message_objects.register_updated_property(property)


@event.listens_for(Owner, 'after_update')
@event.listens_for(Owner, 'after_insert')
@event.listens_for(Owner, 'after_delete')
def on_owner_change(mapper, connection, target):
    logger.debug('Owner change event emitted')
    for ownership in target.ownerships:
        message_objects.register_updated_property(ownership.property)


@event.listens_for(TransportStation, 'after_update')
@event.listens_for(TransportStation, 'after_insert')
@event.listens_for(TransportStation, 'after_delete')
def on_owner_change(mapper, connection, target):
    logger.debug('Transport station change event emitted')
    for assoc in target.transport_station_assocs:
        message_objects.register_updated_property(assoc.property)


@event.listens_for(TransportStationProperty, 'after_update')
@event.listens_for(TransportStationProperty, 'after_insert')
@event.listens_for(TransportStationProperty, 'after_delete')
def on_owner_change(mapper, connection, target):
    logger.debug('TransportStation-property change event emitted')
    property = _get_property_object(target)
    message_objects.register_updated_property(property)


@event.listens_for(MicroMarket, 'after_update')
@event.listens_for(Locality, 'after_update')
@event.listens_for(State, 'after_update')
@event.listens_for(CityAlias, 'after_insert')
@event.listens_for(CityAlias, 'after_update')
@event.listens_for(CityAlias, 'after_delete')
def on_property_location_change(mapper, connection, target):
    logger.debug('Micromarket/ Locality, State/ City Alias updated')


@event.listens_for(City, 'after_insert')
def on_city_insert(mapper, connection, target):
    logger.debug('City insert event')
    message_objects.register_new_city(target)


@event.listens_for(City, 'after_update')
def on_city_update(mapper, connection, target):
    logger.debug('City update event')
    message_objects.register_updated_city(target)


@event.listens_for(Room, 'after_insert')
def on_room_insert(mapper, connection, target):
    logger.debug('Room insert event emitted')
    message_objects.register_new_room(target)


@event.listens_for(Room, 'after_update')
def on_room_update(mapper, connection, target):
    logger.debug('Room update event emitted')
    message_objects.register_updated_room(target)


@event.listens_for(RoomTypeConfiguration, 'after_insert')
def on_room_config_insert(mapper, connection, target):
    logger.debug('Room config insert event emitted')
    message_objects.register_new_room_config(target)


@event.listens_for(RoomTypeConfiguration, 'after_update')
def on_room_config_update(mapper, connection, target):
    logger.debug('Room config update event emitted')
    message_objects.register_updated_room_config(target)
    for image in target.property_images:
        message_objects.register_updated_property_image(image)


@event.listens_for(RoomTypeConfiguration, 'after_delete')
def on_room_config_delete(mapper, connection, target):
    logger.debug('Room config delete event emitted')
    message_objects.register_deleted_room_config(target)


@event.listens_for(Restaurant, 'after_insert')
def on_restaurant_create(mapper, connection, target):
    logger.debug('Restaurant create event emitted')
    message_objects.register_new_restaurant(target)


@event.listens_for(Restaurant, 'after_update')
def on_restaurant_update(mapper, connection, target):
    logger.debug('Restaurant update event emitted')
    message_objects.register_updated_restaurant(target)


@event.listens_for(Restaurant, 'after_delete')
def on_restaurant_delete(mapper, connection, target):
    logger.debug('Restaurant delete event emitted')
    message_objects.register_deleted_restaurant(target)


@event.listens_for(Bar, 'after_insert')
def on_bar_create(mapper, connection, target):
    logger.debug('Bar create event emitted')
    message_objects.register_new_bar(target)


@event.listens_for(Bar, 'after_update')
def on_bar_update(mapper, connection, target):
    logger.debug('Bar update event emitted')
    message_objects.register_updated_bar(target)


@event.listens_for(Bar, 'after_delete')
def on_bar_delete(mapper, connection, target):
    logger.debug('Bar delete event emitted')
    message_objects.register_deleted_bar(target)


@event.listens_for(BanquetHall, 'after_insert')
def on_hall_create(mapper, connection, target):
    logger.debug('Banquet Hall create event emitted')
    message_objects.register_new_hall(target)


@event.listens_for(BanquetHall, 'after_update')
def on_hall_update(mapper, connection, target):
    logger.debug('Banquet Hall update event emitted')
    message_objects.register_updated_hall(target)


@event.listens_for(BanquetHall, 'after_delete')
def on_hall_delete(mapper, connection, target):
    logger.debug('Banquet Hall delete event emitted')
    message_objects.register_deleted_hall(target)


@event.listens_for(PropertyAmenity, 'after_insert')
def on_property_amenity_insert(mapper, connection, target):
    logger.debug('Property Amenity insert event emitted')
    message_objects.register_new_property_amenity(target)


@event.listens_for(PropertyAmenity, 'after_update')
def on_property_amenity_update(mapper, connection, target):
    logger.debug('Property Amenity update event emitted')
    message_objects.register_updated_property_amenity(target)


@event.listens_for(RoomAmenity, 'after_insert')
def on_room_amenity_insert(mapper, connection, target):
    logger.debug('Property Amenity insert event emitted')
    room = _get_room_object(target)
    message_objects.register_new_room_amenities(target)


@event.listens_for(RoomAmenity, 'after_update')
def on_room_amenity_update(mapper, connection, target):
    logger.debug('Property Amenity update event emitted')
    room = _get_room_object(target)
    message_objects.register_updated_room_amenities(target)


@event.listens_for(AmenityPublicWashroom, 'after_insert')
@event.listens_for(AmenityPublicWashroom, 'after_update')
@event.listens_for(AmenityPublicWashroom, 'after_delete')
@event.listens_for(AmenityElevator, 'after_insert')
@event.listens_for(AmenityElevator, 'after_update')
@event.listens_for(AmenityElevator, 'after_delete')
@event.listens_for(AmenityParking, 'after_insert')
@event.listens_for(AmenityParking, 'after_update')
@event.listens_for(AmenityParking, 'after_delete')
@event.listens_for(AmenityDisableFriendly, 'after_insert')
@event.listens_for(AmenityDisableFriendly, 'after_update')
@event.listens_for(AmenityDisableFriendly, 'after_delete')
@event.listens_for(AmenitySwimmingPool, 'after_insert')
@event.listens_for(AmenitySwimmingPool, 'after_update')
@event.listens_for(AmenitySwimmingPool, 'after_delete')
@event.listens_for(AmenityGym, 'after_insert')
@event.listens_for(AmenityGym, 'after_update')
@event.listens_for(AmenityGym, 'after_delete')
@event.listens_for(AmenityPrivateCab, 'after_insert')
@event.listens_for(AmenityPrivateCab, 'after_update')
@event.listens_for(AmenityPrivateCab, 'after_delete')
@event.listens_for(AmenitySpa, 'after_insert')
@event.listens_for(AmenitySpa, 'after_update')
@event.listens_for(AmenitySpa, 'after_delete')
@event.listens_for(AmenityLaundry, 'after_insert')
@event.listens_for(AmenityLaundry, 'after_update')
@event.listens_for(AmenityLaundry, 'after_delete')
@event.listens_for(AmenityBreakfast, 'after_insert')
@event.listens_for(AmenityBreakfast, 'after_update')
@event.listens_for(AmenityBreakfast, 'after_delete')
@event.listens_for(AmenityPayment, 'after_insert')
@event.listens_for(AmenityPayment, 'after_update')
@event.listens_for(AmenityPayment, 'after_delete')
def on_property_sub_amenity_change(mapper, connection, target):
    logger.debug('Property Amenity update event emitted')
    if target.property_amenity:
        message_objects.register_updated_property_amenity(target.property_amenity)


@event.listens_for(AmenityIntercom, 'after_insert')
@event.listens_for(AmenityIntercom, 'after_update')
@event.listens_for(AmenityIntercom, 'after_delete')
@event.listens_for(AmenityHotWater, 'after_insert')
@event.listens_for(AmenityHotWater, 'after_update')
@event.listens_for(AmenityHotWater, 'after_delete')
@event.listens_for(AmenityTV, 'after_insert')
@event.listens_for(AmenityTV, 'after_update')
@event.listens_for(AmenityTV, 'after_delete')
@event.listens_for(AmenityAC, 'after_insert')
@event.listens_for(AmenityAC, 'after_update')
@event.listens_for(AmenityAC, 'after_delete')
@event.listens_for(AmenityHeater, 'after_insert')
@event.listens_for(AmenityHeater, 'after_update')
@event.listens_for(AmenityHeater, 'after_delete')
@event.listens_for(AmenityStove, 'after_insert')
@event.listens_for(AmenityStove, 'after_update')
@event.listens_for(AmenityStove, 'after_delete')
@event.listens_for(AmenityTwinBed, 'after_insert')
@event.listens_for(AmenityTwinBed, 'after_update')
@event.listens_for(AmenityTwinBed, 'after_delete')
def on_property_room_sub_amenity_change(mapper, connection, target):
    logger.debug('Property Amenity update event emitted')
    if target.room_amenity:
        message_objects.register_updated_room_amenities(target.room_amenity)


@event.listens_for(PropertyImage, 'after_insert')
def on_property_image_add(mapper, connection, target):
    message_objects.register_new_property_image(target)


@event.listens_for(PropertyImage, 'after_update')
def on_property_image_update(mapper, connection, target):
    message_objects.register_updated_property_image(target)


@event.listens_for(PropertyImage, 'after_delete')
def on_property_image_delete(mapper, connection, target):
    message_objects.register_deleted_property_image(target)


@event.listens_for(PropertyVideo, 'after_insert')
def on_property_video_add(mapper, connection, target):
    message_objects.register_new_property_video(target)


@event.listens_for(PropertyVideo, 'after_update')
def on_property_video_update(mapper, connection, target):
    message_objects.register_updated_property_video(target)


@event.listens_for(PropertyVideo, 'after_delete')
def on_property_video_delete(mapper, connection, target):
    message_objects.register_deleted_property_video(target)


@event.listens_for(AmenitySummary, 'after_insert')
@event.listens_for(AmenitySummary, 'after_update')
def on_amenity_summary_update(mapper, connection, target):
    message_objects.register_amenity_summary(target)


@event.listens_for(SkuCategory, 'after_insert')
def on_sku_category_insert(mapper, connection, target):
    logger.debug('Sku Category insert event')
    # add message
    message_objects.register_sku_category(target)


@event.listens_for(SkuCategory, 'after_update')
def on_sku_category_update(mapper, connection, target):
    logger.debug('Sku Category update event')
    # add message
    message_objects.register_updated_sku_category(target)


@event.listens_for(Channel, 'after_insert')
def on_channel_insert(mapper, connection, target):
    logger.debug('Channel insert event')
    message_objects.register_new_channel(target)


@event.listens_for(Channel, 'after_update')
def on_channel_update(mapper, connection, target):
    logger.debug('Channel update event')
    message_objects.register_updated_channel(target)


@event.listens_for(Channel, 'after_delete')
def on_channel_delete(mapper, connection, target):
    logger.debug('Channel delete event')
    message_objects.register_deleted_channel(target)


@event.listens_for(SubChannel, 'after_insert')
def on_channel_insert(mapper, connection, target):
    logger.debug('SubChannel insert event')
    message_objects.register_new_sub_channel(target)


@event.listens_for(SubChannel, 'after_update')
def on_channel_update(mapper, connection, target):
    logger.debug('SubChannel update event')
    message_objects.register_updated_sub_channel(target)
    channel = _get_channel_object(target)
    message_objects.register_updated_channel(channel)


@event.listens_for(SubChannel, 'after_delete')
def on_channel_delete(mapper, connection, target):
    logger.debug('SubChannel delete event')
    message_objects.register_deleted_sub_channel(target)


@event.listens_for(Application, 'after_insert')
def on_application_insert(mapper, connection, target):
    logger.debug('Application insert event')
    message_objects.register_new_application(target)


@event.listens_for(Application, 'after_update')
def on_application_update(mapper, connection, target):
    logger.debug('Application update event')
    message_objects.register_updated_application(target)


@event.listens_for(Application, 'after_delete')
def on_application_delete(mapper, connection, target):
    logger.debug('Application delete event')
    message_objects.register_deleted_application(target)


@event.listens_for(RoomType, 'after_insert')
def on_room_type_insert(mapper, connection, target):
    logger.debug('Room Type insert event')
    message_objects.register_new_room_type(target)


@event.listens_for(RoomType, 'after_update')
def on_room_type_update(mapper, connection, target):
    logger.debug('Room Type update event')
    message_objects.register_updated_room_type(target)


@event.listens_for(Provider, 'after_update')
@event.listens_for(Provider, 'after_insert')
@event.listens_for(Provider, 'after_delete')
def on_provider_change(mapper, connection, target):
    logger.debug('Provider change event emitted')


@event.listens_for(PropertySku, 'after_insert')
@event.listens_for(PropertySku, 'after_update')
def on_property_sku_change(mapper, connection, target):
    logger.debug('PropertySku after insert and update event')
    service_provider.sku_service.publish_sku_under_property([target.id])


@event.listens_for(Sku, 'after_insert')
def on_sku_insert(mapper, connection, target):
    if not target.code:
        Sku.fill_code(mapper, connection, target)
    logger.debug('Sku insert event')
    if hasattr(app_context, 'is_expense_item_sku') and app_context.is_expense_item_sku:
        service_provider.sku_service.publish_sku([target.id])
        app_context.is_expense_item_sku = False
        return


@event.listens_for(Sku, 'after_update')
def on_sku_update(mapper, connection, target):
    logger.debug('Sku update event')


@event.listens_for(RoomRackRateModel, 'before_insert')
def before_room_rack_rate_insert(mapper, connection, target):
    logger.debug('RoomRackRateModel before insert event')
    target.fill_room_rack_rate_id()


@event.listens_for(RoomRackRateModel, 'after_insert')
@event.listens_for(RoomRackRateModel, 'after_update')
def after_room_rack_rate_udpate(mapper, connection, target):
    logger.debug('RoomRackRateModel after insert and update event')
    service_provider.room_service.publish_room_rack_rates([target.room_rack_rate_id])


@event.listens_for(PricingPolicy, 'after_insert')
@event.listens_for(PricingPolicy, 'after_update')
def on_pricing_policy_change(mapper, connection, target):
    logger.debug('Pricing policy change event')
    message_objects.register_updated_pricing_policy(target)


@event.listens_for(Seller, 'after_insert')
def on_seller_details_change(mapper, connection, target):
    logger.debug('Seller details change event')
    message_objects.register_new_sellers(target)


@event.listens_for(Seller, 'after_update')
def on_seller_details_change(mapper, connection, target):
    logger.debug('Seller details change event')
    message_objects.register_updated_sellers(target)


@event.listens_for(RuptubLegalEntityDetails, 'after_insert')
def on_ruptubdetails_insert(mapper, connection, target):
    logger.debug('Ruptub details insert event emitted')
    message_objects.register_new_ruptub_details(target)


@event.listens_for(RuptubLegalEntityDetails, 'after_update')
def on_ruptubdetails_update(mapper, connection, target):
    logger.debug('Property update event emitted')
    message_objects.register_updated_ruptub_details(target)


@event.listens_for(PricingMapping, 'after_insert')
@event.listens_for(PricingMapping, 'after_update')
def on_pricing_mapping_change(mapper, connection, target):
    logger.debug('Pricing Mapping update event')
    channel_object = _get_channel_object(target)
    message_objects.register_updated_channel(channel_object)


@event.listens_for(ProviderBrandMapping, 'after_update')
@event.listens_for(ProviderBrandMapping, 'after_insert')
@event.listens_for(ProviderBrandMapping, 'after_delete')
def on_provider_brand_change(mapper, connection, target):
    logger.debug('ProviderBrandMapping change event emitted')


@event.listens_for(PropertyBrandMapping, 'after_update')
@event.listens_for(PropertyBrandMapping, 'after_insert')
@event.listens_for(PropertyBrandMapping, 'after_delete')
def on_property_brand_change(mapper, connection, target):
    logger.debug('PropertyBrandMapping change event emitted')
    if target:
        message_objects.register_updated_property(target.property)


@event.listens_for(Session, 'before_commit')
def before_commit(session):
    logger.debug('Commit called, forcing flush')
    session.flush()  # Force flush to prevent commit without flush
    logger.debug('Flush done, creating messages')

    try:
        new_objects, modified_objects, deleted_objects = message_objects.get_message_objects()
        messaging_service.create_publishable_messages(new_objects, modified_objects, deleted_objects, queue_messages)
    except Exception as exception:
        logger.error('Error while creating publishable messages before commit. %s' % repr(exception))
        Utils.send_email()


@event.listens_for(Session, 'after_commit')
def after_commit(session):
    logger.debug('Commit done, Publishing messages')

    try:
        disable_rmq_events = getattr(request_context, 'disable_rmq_events', False)
        if not disable_rmq_events:
            messaging_service.publish_all_messages(queue_messages)
    except Exception as exception:
        logger.error('Error while publishing messages after commit. %s' % repr(exception))
        Utils.send_email()

    message_objects.purge()
    queue_messages.purge()
    logger.debug('Messages Purged post commit')


@event.listens_for(Session, 'after_rollback')
def after_rollback(session):
    logger.debug('DB rollback done')
    message_objects.purge()
    queue_messages.purge()
    logger.debug('Messages Purged post rollback')


def _validate_sku_object(target):
    return service_provider.sku_service.validate_sku_object(target)


def _get_channel_object(target):
    if target.channel:
        return target.channel
    elif target.channel_id:
        return channel_repository.get_channel(target.channel_id)


def _get_property_object(target):
    if target.property:
        return target.property
    elif target.property_id:
        return property_repository.get_property(target.property_id)


def _get_room_object(target):
    if target.room:
        return target.room
    elif target.room_id:
        return property_repository.get_room(target.room_id)


def _get_seller_sku_object(target):
    return seller_sku_repository.get_seller(target.seller_id)
