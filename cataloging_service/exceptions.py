from cataloging_service.constants import error_codes


class CatalogingServiceException(Exception):
    def __init__(self, error_code=error_codes.GENERIC_EXCEPTION, *args, **kwargs):
        super(CatalogingServiceException, self).__init__(*args)
        self.error_details = error_codes.get(error_code)
        self.context = kwargs.get('context', None)
        self.force_commit = kwargs.get('force_commit', False)
        self.send_error_mail = kwargs.get('send_error_mail', True)

    def get_error_message(self):
        return self.error_details['msg']

    def get_error_context(self):
        return self.context

    def get_response_code(self):
        return self.error_details['status_code']

    def __str__(self):
        return 'CS-Exception(%s-%s): %s' % (self.error_details['code'], self.error_details['msg'], self.context)


class ExternalClientException(Exception):
    def __init__(self, message, status_code):
        super(ExternalClientException, self).__init__()
        self.message = message
        self.status_code = status_code


class ValidationException(Exception):
    def __init__(self, error_code, error_message):
        self.error_code = error_code
        self.error_details = error_codes.get(error_code)
        self.error_message = error_message

    def __str__(self):
        return '%s-%s, %s' % (self.error_details['code'], self.error_details['msg'], self.error_message)


class InvalidStateIdException(Exception):
    def __init__(self, message):
        self.message = message

    def __str__(self):
        return self.message


class InvalidTenantException(Exception):
    message = "Tenant Id is invalid"


class DatabaseError(Exception):
    message = "Something went wrong with the database"


class PrimaryKeyCollision(DatabaseError):
    message = "Collision in id"


class DatabaseLockError(DatabaseError):
    message = "You're operating on old version of data, please refresh and retry."
