# -*- coding: utf-8 -*-
from cataloging_service.monkey_patch_py12 import *
from treebo_commons.multitenancy.sqlalchemy import db_engine

__version__ = '0.1.0'
__version_info__ = tuple([int(num) if num.isdigit() else num for num in __version__.replace('-', '.', 1).split('.')])

from cataloging_service.infrastructure import redis_cache

db_engine.setup_tenant_sessions()
redis_cache.setup_tenant_cache()
