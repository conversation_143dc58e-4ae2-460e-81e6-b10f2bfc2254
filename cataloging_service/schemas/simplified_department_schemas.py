"""
Simplified Pydantic schemas for department/profit center architecture
Provides request/response validation for the simplified 3-table approach
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from pydantic.types import PositiveInt

# Import the swag_schema decorator for OpenAPI registration
from cataloging_service.common.schema_registry import swag_schema


class BaseSchema(BaseModel):
    """Base schema for all entities"""
    
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        str_strip_whitespace=True,
    )


# ============================================================================
# Department Template Schemas
# ============================================================================

@swag_schema
class DepartmentTemplateCreateSchema(BaseSchema):
    """Schema for creating department templates"""
    
    brand_id: PositiveInt = Field(..., description="Brand ID this template belongs to")
    code: str = Field(..., min_length=2, max_length=50, description="Unique department code")
    name: str = Field(..., min_length=1, max_length=255, description="Department name")
    financial_code: Optional[str] = Field(None, max_length=20, description="Financial/GL code")
    parent_code: Optional[str] = Field(None, max_length=50, description="Parent department code")
    description: Optional[str] = Field(None, max_length=1000, description="Department description")
    auto_create_on_property_launch: bool = Field(True, description="Auto-create on property launch")
    is_active: bool = Field(True, description="Whether template is active")


@swag_schema
class DepartmentTemplateResponseSchema(BaseSchema):
    """Schema for department template responses"""
    
    id: PositiveInt
    brand_id: PositiveInt
    code: str
    name: str
    financial_code: Optional[str] = None
    parent_code: Optional[str] = None
    description: Optional[str] = None
    auto_create_on_property_launch: bool
    is_active: bool
    created_at: datetime
    modified_at: datetime


# ============================================================================
# Profit Center Template Schemas
# ============================================================================

@swag_schema
class ProfitCenterTemplateCreateSchema(BaseSchema):
    """Schema for creating profit center templates"""
    
    brand_id: PositiveInt = Field(..., description="Brand ID this template belongs to")
    code: str = Field(..., min_length=2, max_length=50, description="Unique profit center code")
    name: str = Field(..., min_length=1, max_length=255, description="Profit center name")
    department_template_code: str = Field(..., max_length=50, description="Associated department template code")
    system_interface: Optional[str] = Field(None, max_length=100, description="POS system interface")
    description: Optional[str] = Field(None, max_length=1000, description="Profit center description")
    auto_create_on_property_launch: bool = Field(True, description="Auto-create on property launch")
    is_active: bool = Field(True, description="Whether template is active")


@swag_schema
class ProfitCenterTemplateResponseSchema(BaseSchema):
    """Schema for profit center template responses"""
    
    id: PositiveInt
    brand_id: PositiveInt
    code: str
    name: str
    department_template_code: str
    system_interface: Optional[str] = None
    description: Optional[str] = None
    auto_create_on_property_launch: bool
    is_active: bool
    created_at: datetime
    modified_at: datetime


# ============================================================================
# Department Schemas (Property Level)
# ============================================================================

@swag_schema
class DepartmentCreateSchema(BaseSchema):
    """Schema for creating property departments"""
    
    property_id: str = Field(..., min_length=1, max_length=50, description="Property ID")
    code: str = Field(..., min_length=2, max_length=50, description="Unique department code within property")
    name: str = Field(..., min_length=1, max_length=255, description="Department name")
    template_code: Optional[str] = Field(None, max_length=50, description="Source template code")
    financial_code: Optional[str] = Field(None, max_length=20, description="Financial/GL code")
    parent_id: Optional[PositiveInt] = Field(None, description="Parent department ID")
    description: Optional[str] = Field(None, max_length=1000, description="Department description")
    is_custom: bool = Field(False, description="Whether department is custom (not from template)")
    is_active: bool = Field(True, description="Whether department is active")


@swag_schema
class DepartmentResponseSchema(BaseSchema):
    """Schema for department responses"""
    
    id: PositiveInt
    property_id: str
    code: str
    name: str
    template_code: Optional[str] = None
    financial_code: Optional[str] = None
    parent_id: Optional[PositiveInt] = None
    description: Optional[str] = None
    is_custom: bool
    is_active: bool
    created_at: datetime
    modified_at: datetime


# ============================================================================
# Enhanced SKU Schemas
# ============================================================================

@swag_schema
class SkuUpdateSchema(BaseSchema):
    """Schema for updating SKU with department/profit center template references"""
    
    default_department_template_code: Optional[str] = Field(None, max_length=50, description="Default department template code")
    profit_center_template_code: Optional[str] = Field(None, max_length=50, description="Profit center template code")
    auto_create_seller_sku: Optional[bool] = Field(None, description="Auto-create seller SKU from template")


@swag_schema
class PropertySkuUpdateSchema(BaseSchema):
    """Schema for updating property SKU with department assignment"""
    
    department_id: Optional[PositiveInt] = Field(None, description="Department ID for this property SKU")


@swag_schema
class SellerUpdateSchema(BaseSchema):
    """Schema for updating seller with profit center enhancements"""
    
    department_id: Optional[PositiveInt] = Field(None, description="Department ID")
    created_from_template_code: Optional[str] = Field(None, max_length=50, description="Source template code")
    system_interface: Optional[str] = Field(None, max_length=100, description="POS system interface")
    is_auto_created: Optional[bool] = Field(None, description="Whether auto-created from template")


@swag_schema
class SellerSkuUpdateSchema(BaseSchema):
    """Schema for updating seller SKU with department assignment"""
    
    department_id: Optional[PositiveInt] = Field(None, description="Department ID (from SKU template, not seller)")


# ============================================================================
# Response Schemas for Enhanced Models
# ============================================================================

@swag_schema
class EnhancedSkuResponseSchema(BaseSchema):
    """Response schema for SKU with department/profit center template references"""
    
    id: PositiveInt
    code: str
    name: str
    default_department_template_code: Optional[str] = None
    profit_center_template_code: Optional[str] = None
    auto_create_seller_sku: bool


@swag_schema
class EnhancedPropertySkuResponseSchema(BaseSchema):
    """Response schema for property SKU with department assignment"""
    
    id: PositiveInt
    sku_id: PositiveInt
    property_id: str
    department_id: Optional[PositiveInt] = None


@swag_schema
class EnhancedSellerResponseSchema(BaseSchema):
    """Response schema for seller with profit center enhancements"""
    
    id: PositiveInt
    name: str
    property_id: str
    department_id: Optional[PositiveInt] = None
    created_from_template_code: Optional[str] = None
    system_interface: Optional[str] = None
    is_auto_created: bool


@swag_schema
class EnhancedSellerSkuResponseSchema(BaseSchema):
    """Response schema for seller SKU with department assignment"""
    
    id: PositiveInt
    seller_id: PositiveInt
    sku_id: PositiveInt
    department_id: Optional[PositiveInt] = None


# ============================================================================
# Bulk Operation Schemas
# ============================================================================

@swag_schema
class BulkDepartmentCreateSchema(BaseSchema):
    """Schema for bulk creating departments from templates"""
    
    property_id: str = Field(..., description="Property ID")
    template_codes: list[str] = Field(..., description="List of department template codes to create")
    auto_create_profit_centers: bool = Field(True, description="Also create associated profit centers")


@swag_schema
class BulkOperationResponseSchema(BaseSchema):
    """Response schema for bulk operations"""
    
    success_count: int = Field(..., description="Number of successful operations")
    error_count: int = Field(..., description="Number of failed operations")
    errors: list[Dict[str, Any]] = Field(default_factory=list, description="List of errors encountered")
    created_ids: list[PositiveInt] = Field(default_factory=list, description="List of created entity IDs")
