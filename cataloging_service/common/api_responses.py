"""
API response handler for consistent responses across the cataloging service.
Replaces the complex error_handlers.py and response_utils.py with a clean, focused solution.
"""

import logging
import uuid
from functools import wraps
from http import HTTPStatus
from typing import Any, Optional, List

from flask import jsonify
from pydantic import ValidationError

logger = logging.getLogger(__name__)


class ApiResponse:
    """
    Unified API response handler that provides consistent response formatting
    with automatic Pydantic model serialization and error handling.
    """

    @staticmethod
    def _serialize_data(data: Any) -> Any:
        """Automatically serialize Pydantic models and lists of models"""
        if isinstance(data, list):
            if data and hasattr(data[0], "model_dump"):
                return [item.model_dump() for item in data]
            return data
        elif hasattr(data, "model_dump"):
            return data.model_dump()
        return data

    @staticmethod
    def success(data: Any, status_code: HTTPStatus = HTTPStatus.OK) -> tuple:
        """
        Create a success response with automatic Pydantic serialization

        Args:
            data: Response data (Pydantic models are automatically serialized)
            status_code: HTTP status code

        Returns:
            Tuple of (jsonified_response, status_code)
        """
        serialized_data = ApiResponse._serialize_data(data)
        return jsonify(serialized_data), status_code

    @staticmethod
    def created(data: Any) -> tuple:
        """201 Created response"""
        return ApiResponse.success(data, HTTPStatus.CREATED)

    @staticmethod
    def no_content() -> tuple:
        """204 No Content response"""
        return "", HTTPStatus.NO_CONTENT

    @staticmethod
    def validation_error(_validation_error: ValidationError) -> tuple:
        """
        Handle Pydantic validation errors with detailed field information

        Args:
            _validation_error: Pydantic ValidationError instance

        Returns:
            Tuple of (jsonified_response, status_code)
        """
        errors = []
        for error in _validation_error.errors():
            field_path = ".".join(str(loc) for loc in error["loc"])
            errors.append(
                {
                    "field": field_path,
                    "message": error["msg"],
                    "invalid_value": error.get("input"),
                    "type": error.get("type"),
                }
            )

        response_data = {
            "error": "Validation Failed",
            "message": f"Request validation failed with {len(errors)} error(s)",
            "validation_errors": errors,
            "request_id": str(uuid.uuid4()),
        }

        return jsonify(response_data), HTTPStatus.BAD_REQUEST

    @staticmethod
    def not_found(resource_type: str, resource_id: Any) -> tuple:
        """404 Not Found response"""
        response_data = {
            "error": "Not Found",
            "message": f"{resource_type} not found",
            "details": f"{resource_type} with ID '{resource_id}' does not exist",
            "request_id": str(uuid.uuid4()),
        }
        return jsonify(response_data), HTTPStatus.NOT_FOUND

    @staticmethod
    def conflict(resource_type: str, reason: str) -> tuple:
        """409 Conflict response"""
        response_data = {
            "error": "Conflict",
            "message": f"{resource_type} conflict",
            "details": reason,
            "request_id": str(uuid.uuid4()),
        }
        return jsonify(response_data), HTTPStatus.CONFLICT

    @staticmethod
    def server_error(
        exception: Optional[Exception] = None, include_details: bool = False
    ) -> tuple:
        """
        500 Internal Server Error response

        Args:
            exception: Optional exception that caused the error
            include_details: Whether to include exception details (for debugging)
        """
        request_id = str(uuid.uuid4())

        response_data = {
            "error": "Internal Server Error",
            "message": "An unexpected error occurred",
            "request_id": request_id,
        }

        if exception and include_details:
            response_data["details"] = str(exception)

        # Log the full error for debugging
        if exception:
            logger.error(
                f"Internal server error [{request_id}]: {str(exception)}", exc_info=True
            )

        return jsonify(response_data), HTTPStatus.INTERNAL_SERVER_ERROR

    @staticmethod
    def paginated(data: List[Any], page: int, per_page: int, total: int) -> tuple:
        """
        Create a paginated response with metadata

        Args:
            data: List of items for current page
            page: Current page number (1-based)
            per_page: Items per page
            total: Total number of items
        """
        serialized_data = ApiResponse._serialize_data(data)
        total_pages = (total + per_page - 1) // per_page

        response_data = {
            "data": serialized_data,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1,
            },
        }

        return jsonify(response_data), HTTPStatus.OK


def api_endpoint(func):
    """
    Decorator that provides automatic error handling for API endpoints.
    Catches common exceptions and returns appropriate responses.

    Usage:
        @api_endpoint
        def my_endpoint():
            # Your endpoint logic here
            return ApiResponse.success(data)
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValidationError as e:
            return ApiResponse.validation_error(e)
        except Exception as e:
            # Log unexpected errors and return generic server error
            logger.exception(f"Unexpected error in {func.__name__}: {str(e)}")
            return ApiResponse.server_error(e, include_details=False)

    return wrapper


# Convenience aliases for backward compatibility and ease of use
success = ApiResponse.success
created = ApiResponse.created
no_content = ApiResponse.no_content
validation_error = ApiResponse.validation_error
not_found = ApiResponse.not_found
conflict = ApiResponse.conflict
server_error = ApiResponse.server_error
paginated = ApiResponse.paginated


# Export the main classes and functions
__all__ = [
    "ApiResponse",
    "api_endpoint",
    "success",
    "created",
    "no_content",
    "validation_error",
    "not_found",
    "conflict",
    "server_error",
    "paginated",
]
