"""
Request Decorators for API Endpoints
Elegant decorators for handling request parsing, validation, and URL parameter injection.
"""

import inspect
from functools import wraps
from typing import Type, Dict, Any, get_type_hints
from flask import request
from pydantic import BaseModel, ValidationError
from cataloging_service.common.api_responses import ApiResponse


def parse_request(
    body_schema: Type[BaseModel] = None,
    query_schema: Type[BaseModel] = None,
    inject_path_params: bool = True,
):
    """
    Decorator that parses and validates request data using Pydantic schemas.

    Features:
    - Validates request body against body_schema
    - Validates query parameters against query_schema
    - Injects URL path parameters into the schema
    - Handles type conversion for path parameters
    - Provides automatic error responses for validation failures

    Args:
        body_schema: Pydantic schema for request body validation
        query_schema: Pydantic schema for query parameter validation
        inject_path_params: Whether to inject URL path parameters into schemas

    Usage:
        @parse_request(body_schema=CreateSchema, query_schema=FilterSchema)
        def my_endpoint(data: CreateSchema, filters: FilterSchema, property_id: str):
            # data and filters are validated Pydantic instances
            # property_id is automatically injected from URL path
            pass
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # Get function signature to understand parameter names and types
                sig = inspect.signature(func)
                type_hints = get_type_hints(func)

                # Parse request body if schema provided
                if body_schema:
                    body_data = request.json or {}

                    # Inject path parameters into body data if requested
                    if inject_path_params:
                        body_data.update(_extract_path_params(kwargs, body_schema))

                    # Validate and create schema instance
                    validated_body = body_schema.model_validate(body_data)

                    # Find the parameter that expects this schema type
                    body_param_name = _find_param_for_type(sig, body_schema)
                    if body_param_name:
                        kwargs[body_param_name] = validated_body

                # Parse query parameters if schema provided
                if query_schema:
                    query_data = _parse_query_params(request.args, query_schema)

                    # Inject path parameters into query data if requested
                    if inject_path_params:
                        query_data.update(_extract_path_params(kwargs, query_schema))

                    # Validate and create schema instance
                    validated_query = query_schema.model_validate(query_data)

                    # Find the parameter that expects this schema type
                    query_param_name = _find_param_for_type(sig, query_schema)
                    if query_param_name:
                        kwargs[query_param_name] = validated_query

                # Convert path parameters to correct types
                if inject_path_params:
                    kwargs = _convert_path_param_types(kwargs, sig, type_hints)

                return func(*args, **kwargs)

            except ValidationError as e:
                return ApiResponse.validation_error(e)
            except Exception as e:
                return ApiResponse.server_error(e)

        return wrapper

    return decorator


def _extract_path_params(
    kwargs: Dict[str, Any], schema: Type[BaseModel]
) -> Dict[str, Any]:
    """Extract path parameters that match schema fields"""
    schema_fields = set(schema.model_fields.keys())
    path_params = {}

    for key, value in kwargs.items():
        if key in schema_fields:
            path_params[key] = value

    return path_params


def _find_param_for_type(sig: inspect.Signature, target_type: Type) -> str:
    """Find parameter name that expects the target type"""
    for param_name, param in sig.parameters.items():
        if param.annotation == target_type:
            return param_name
    return None


def _parse_query_params(args, schema: Type[BaseModel]) -> Dict[str, Any]:
    """Parse Flask request args into a dictionary suitable for Pydantic validation"""
    query_data = {}
    schema_fields = schema.model_fields

    for field_name, field_info in schema_fields.items():
        if field_name in args:
            value = args.get(field_name)

            # Handle boolean conversion
            if field_info.annotation == bool or (
                hasattr(field_info.annotation, "__origin__")
                and field_info.annotation.__origin__ is type(None)
                and bool in field_info.annotation.__args__
            ):
                if isinstance(value, str):
                    query_data[field_name] = value.lower() in ("true", "1", "yes", "on")
                else:
                    query_data[field_name] = bool(value)

            # Handle integer conversion
            elif field_info.annotation == int or (
                hasattr(field_info.annotation, "__origin__")
                and field_info.annotation.__origin__ is type(None)
                and int in field_info.annotation.__args__
            ):
                try:
                    query_data[field_name] = int(value) if value else None
                except (ValueError, TypeError):
                    query_data[
                        field_name
                    ] = value  # Let Pydantic handle the validation error

            # Handle string and other types
            else:
                query_data[field_name] = value

    return query_data


def _convert_path_param_types(
    kwargs: Dict[str, Any], sig: inspect.Signature, type_hints: Dict[str, Type]
) -> Dict[str, Any]:
    """Convert path parameters to their expected types based on function signature"""
    converted_kwargs = kwargs.copy()

    for param_name, param in sig.parameters.items():
        if param_name in kwargs and param_name in type_hints:
            expected_type = type_hints[param_name]
            current_value = kwargs[param_name]

            # Skip if already correct type or is a Pydantic model
            if isinstance(current_value, expected_type) or isinstance(
                current_value, BaseModel
            ):
                continue

            # Convert to expected type
            try:
                if expected_type == int:
                    converted_kwargs[param_name] = int(current_value)
                elif expected_type == float:
                    converted_kwargs[param_name] = float(current_value)
                elif expected_type == bool:
                    converted_kwargs[param_name] = str(current_value).lower() in (
                        "true",
                        "1",
                        "yes",
                        "on",
                    )
                # Add more type conversions as needed
            except (ValueError, TypeError):
                # Let the function handle the invalid type
                pass

    return converted_kwargs


# Convenience decorators for common patterns
def parse_body(schema: Type[BaseModel]):
    """Convenience decorator for body-only parsing"""
    return parse_request(body_schema=schema)


def parse_query(schema: Type[BaseModel]):
    """Convenience decorator for query-only parsing"""
    return parse_request(query_schema=schema)


def parse_body_and_query(body_schema: Type[BaseModel], query_schema: Type[BaseModel]):
    """Convenience decorator for both body and query parsing"""
    return parse_request(body_schema=body_schema, query_schema=query_schema)


# Export decorators
__all__ = ["parse_request", "parse_body", "parse_query", "parse_body_and_query"]
