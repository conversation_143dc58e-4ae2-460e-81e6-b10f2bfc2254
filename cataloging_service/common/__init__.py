"""
Utility modules for the cataloging service
Contains helper functions and common utilities
"""

# Import the main Utils class from the utils.py file at the cataloging_service level
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
from utils import Utils

# Import the new unified API response handler
from .api_responses import (
    ApiResponse,
    api_endpoint,
    success,
    created,
    no_content,
    validation_error,
    not_found,
    conflict,
    server_error,
    paginated
)

__all__ = [
    'Utils',  # Main Utils class
    'ApiResponse',  # New unified response handler
    'api_endpoint',  # Decorator for automatic error handling
    'success',
    'created',
    'no_content',
    'validation_error',
    'not_found',
    'conflict',
    'server_error',
    'paginated'
]
