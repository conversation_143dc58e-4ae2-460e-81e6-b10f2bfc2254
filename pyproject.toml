[project]
name = "cataloging-service"
version = "0.1.0"
description = "Catalog service codebase for Treebo"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aenum==2.2.6",
    "amqp==5.3.1",
    "apispec==6.0.2",
    "apispec-pydantic-plugin==0.3.0",
    "apispec-webframeworks==0.5.2",
    "bcrypt==3.1.7",
    "boto3==1.34.103",
    "botocore==1.34.103",
    "click==8.1.3",
    "email-validator==1.3.1",
    "factory-boy==2.10.0",
    "flasgger==*******",
    "flask==2.0.1",
    "flask-admin==1.6.0",
    "flask-apispec==0.8.0",
    "flask-caching==1.10.1",
    "flask-login==0.5.0",
    "flask-marshmallow==0.12.0",
    "flask-script==2.0.5",
    "flask-security==3.0.0",
    "flask-wtf==0.15.1",
    "flaskhealthcheck==1.4.4.dev0",
    "freezegun==1.1.0",
    "fuzzywuzzy==0.17.0",
    "gevent==25.5.1",
    "google-api-python-client==1.6.2",
    "greenlet==3.2.2",
    "gunicorn==23.0.0",
    "httplib2==0.20.0",
    "itsdangerous==2.0.1",
    "jinja2==3.0.3",
    "kombu==5.3.4",
    "logstash-formatter==0.5.16",
    "lxml==5.4.0",
    "markupsafe==2.1.1",
    "marshmallow==2.13.5",
    "marshmallow-sqlalchemy==0.12.1",
    "newrelic==8.11.0",
    "pandas==2.2.3",
    "psycopg2-binary==2.9.9",
    "pydantic==2.11.5",
    "python-dateutil==2.8.2",
    "python-levenshtein==0.27.1",
    "python-slugify==1.2.5",
    "pytz==2020.5",
    "pyyaml==5.2",
    "redis==3.3.11",
    "requests==2.31.0",
    "sentry-sdk[flask]==0.10.2",
    "setuptools<=80.0.0",
    "simplejson==3.17.0",
    "six==1.16.0",
    "sqlalchemy==1.4.54",
    "treebo-commons==3.2.6",
    "werkzeug==2.0.3",
    "whitenoise==5.0.1",
    "wtforms==2.3.3",
]

[tool.uv]
index-url = "https://pypi.org/simple"
extra-index-url = ["https://pypi.tree.bo/simple"]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "flask-shell-ipython>=0.5.3",
    "ipython>=9.3.0",
    "pytest==7.4.4",
    "pytest-html==3.2.0",
    "python-dotenv==1.1.0",
    "xlrd==1.1.0",
]
